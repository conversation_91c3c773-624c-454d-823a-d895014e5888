import { createClient } from '@supabase/supabase-js';
import { env } from '../config/env';

export const supabase = createClient(env.supabase.url, env.supabase.anon<PERSON>ey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
});

// Database types for better TypeScript support
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          first_name: string;
          last_name: string;
          role: 'professional' | 'facility';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          first_name: string;
          last_name: string;
          role: 'professional' | 'facility';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          first_name?: string;
          last_name?: string;
          role?: 'professional' | 'facility';
          created_at?: string;
          updated_at?: string;
        };
      };
      healthcare_professionals: {
        Row: {
          id: string;
          user_id: string;
          license_number: string;
          specialization: string;
          experience: number;
          hourly_rate: number;
          certifications: string[];
          profile_image?: string;
          bio?: string;
          rating: number;
          completed_shifts: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          license_number: string;
          specialization: string;
          experience: number;
          hourly_rate: number;
          certifications?: string[];
          profile_image?: string;
          bio?: string;
          rating?: number;
          completed_shifts?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          license_number?: string;
          specialization?: string;
          experience?: number;
          hourly_rate?: number;
          certifications?: string[];
          profile_image?: string;
          bio?: string;
          rating?: number;
          completed_shifts?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      facilities: {
        Row: {
          id: string;
          user_id: string;
          facility_name: string;
          facility_type: 'hospital' | 'clinic' | 'nursing_home' | 'dental_office' | 'school';
          address: {
            street: string;
            city: string;
            state: string;
            zipCode: string;
            country: string;
          };
          contact_phone: string;
          license_number: string;
          rating: number;
          total_shifts_posted: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          facility_name: string;
          facility_type: 'hospital' | 'clinic' | 'nursing_home' | 'dental_office' | 'school';
          address: {
            street: string;
            city: string;
            state: string;
            zipCode: string;
            country: string;
          };
          contact_phone: string;
          license_number: string;
          rating?: number;
          total_shifts_posted?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          facility_name?: string;
          facility_type?: 'hospital' | 'clinic' | 'nursing_home' | 'dental_office' | 'school';
          address?: {
            street: string;
            city: string;
            state: string;
            zipCode: string;
            country: string;
          };
          contact_phone?: string;
          license_number?: string;
          rating?: number;
          total_shifts_posted?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      shifts: {
        Row: {
          id: string;
          facility_id: string;
          title: string;
          description: string;
          specialization: string;
          start_date_time: string;
          end_date_time: string;
          hourly_rate: number;
          status: 'open' | 'booked' | 'completed' | 'cancelled';
          requirements: string[];
          booked_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          facility_id: string;
          title: string;
          description: string;
          specialization: string;
          start_date_time: string;
          end_date_time: string;
          hourly_rate: number;
          status?: 'open' | 'booked' | 'completed' | 'cancelled';
          requirements?: string[];
          booked_by?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          facility_id?: string;
          title?: string;
          description?: string;
          specialization?: string;
          start_date_time?: string;
          end_date_time?: string;
          hourly_rate?: number;
          status?: 'open' | 'booked' | 'completed' | 'cancelled';
          requirements?: string[];
          booked_by?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      bookings: {
        Row: {
          id: string;
          shift_id: string;
          professional_id: string;
          status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
          notes?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          shift_id: string;
          professional_id: string;
          status?: 'pending' | 'confirmed' | 'completed' | 'cancelled';
          notes?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          shift_id?: string;
          professional_id?: string;
          status?: 'pending' | 'confirmed' | 'completed' | 'cancelled';
          notes?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
};
