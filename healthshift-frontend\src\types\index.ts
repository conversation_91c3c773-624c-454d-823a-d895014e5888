export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'professional' | 'facility';
  createdAt: string;
  updatedAt: string;
}

export interface HealthcareProfessional extends User {
  role: 'professional';
  licenseNumber: string;
  specialization: string;
  experience: number;
  hourlyRate: number;
  availability: Availability[];
  certifications: string[];
  profileImage?: string;
  bio?: string;
  rating: number;
  completedShifts: number;
}

export interface Facility extends User {
  role: 'facility';
  facilityName: string;
  facilityType: 'hospital' | 'clinic' | 'nursing_home' | 'dental_office' | 'school';
  address: Address;
  contactPhone: string;
  licenseNumber: string;
  rating: number;
  totalShiftsPosted: number;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface Availability {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
}

export interface Shift {
  id: string;
  facilityId: string;
  facility: Facility;
  title: string;
  description: string;
  specialization: string;
  startDateTime: string;
  endDateTime: string;
  hourlyRate: number;
  status: 'open' | 'booked' | 'completed' | 'cancelled';
  requirements: string[];
  createdAt: string;
  updatedAt: string;
  bookedBy?: string;
  professional?: HealthcareProfessional;
}

export interface Booking {
  id: string;
  shiftId: string;
  shift: Shift;
  professionalId: string;
  professional: HealthcareProfessional;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  notes?: string;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'shift_booked' | 'shift_confirmed' | 'shift_cancelled' | 'new_shift_available' | 'reminder';
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
  relatedId?: string; // ID of related shift, booking, etc.
}

export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ShiftFilters {
  specialization?: string;
  startDate?: string;
  endDate?: string;
  minRate?: number;
  maxRate?: number;
  facilityType?: string;
  location?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}
