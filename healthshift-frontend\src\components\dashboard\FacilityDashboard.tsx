import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { FacilityLayout } from './FacilityLayout';
import { FacilityOverview } from './facility/FacilityOverview';
import { ShiftManagement } from './facility/ShiftManagement';
import { StaffingRequests } from './facility/StaffingRequests';
import { FacilityProfile } from './facility/FacilityProfile';
import { FacilitySettings } from './facility/FacilitySettings';

function FacilityDashboard() {
  return (
    <FacilityLayout>
      <Routes>
        <Route index element={<FacilityOverview />} />
        <Route path="shifts" element={<ShiftManagement />} />
        <Route path="staffing" element={<StaffingRequests />} />
        <Route path="profile" element={<FacilityProfile />} />
        <Route path="settings" element={<FacilitySettings />} />
        <Route path="*" element={<Navigate to="/facility" replace />} />
      </Routes>
    </FacilityLayout>
  );
}

export default FacilityDashboard;
