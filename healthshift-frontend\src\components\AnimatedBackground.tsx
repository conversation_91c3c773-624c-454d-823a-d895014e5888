import React, { useEffect, useRef } from 'react';
import p5 from 'p5';

interface Dot {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  alpha: number;
  targetAlpha: number;
}

const AnimatedBackground: React.FC = () => {
  const sketchRef = useRef<HTMLDivElement>(null);
  const p5Instance = useRef<p5 | null>(null);

  useEffect(() => {
    if (!sketchRef.current) return;

    const sketch = (p: p5) => {
      let dots: Dot[] = [];
      const numDots = 30;
      let time = 0;

      p.setup = () => {
        const canvas = p.createCanvas(p.windowWidth, p.windowHeight);
        canvas.parent(sketchRef.current!);
        canvas.style('position', 'fixed');
        canvas.style('top', '0');
        canvas.style('left', '0');
        canvas.style('z-index', '-1');
        canvas.style('pointer-events', 'none');

        // Initialize dots with CRED-style subtle movement
        for (let i = 0; i < numDots; i++) {
          dots.push({
            x: p.random(p.width),
            y: p.random(p.height),
            vx: p.random(-0.3, 0.3),
            vy: p.random(-0.3, 0.3),
            size: p.random(1, 3),
            alpha: 0,
            targetAlpha: p.random(0.1, 0.3)
          });
        }
      };

      p.draw = () => {
        // CRED-style dark background
        p.background(15, 15, 15); // #0f0f0f

        time += 0.005; // Slower, more subtle movement

        // Update and draw dots
        dots.forEach((dot, index) => {
          // Smooth alpha transition
          dot.alpha = p.lerp(dot.alpha, dot.targetAlpha, 0.02);

          // Subtle movement with sine wave
          dot.x += dot.vx + Math.sin(time + index * 0.5) * 0.1;
          dot.y += dot.vy + Math.cos(time + index * 0.3) * 0.1;

          // Wrap around screen
          if (dot.x < -10) dot.x = p.width + 10;
          if (dot.x > p.width + 10) dot.x = -10;
          if (dot.y < -10) dot.y = p.height + 10;
          if (dot.y > p.height + 10) dot.y = -10;

          // Draw subtle dot
          p.push();
          p.translate(dot.x, dot.y);

          // CRED accent color with low opacity
          p.fill(0, 212, 170, dot.alpha * 255); // #00d4aa
          p.noStroke();
          p.ellipse(0, 0, dot.size);
          p.pop();

          // Occasionally change target alpha for subtle pulsing
          if (p.random() < 0.001) {
            dot.targetAlpha = p.random(0.05, 0.25);
          }
        });

        // Draw very subtle connections between nearby dots
        for (let i = 0; i < dots.length; i++) {
          for (let j = i + 1; j < dots.length; j++) {
            const distance = p.dist(
              dots[i].x, dots[i].y,
              dots[j].x, dots[j].y
            );

            if (distance < 150) {
              const alpha = p.map(distance, 0, 150, 0.1, 0);
              p.stroke(0, 212, 170, alpha * 255);
              p.strokeWeight(0.5);
              p.line(
                dots[i].x, dots[i].y,
                dots[j].x, dots[j].y
              );
            }
          }
        }

        // Add subtle geometric accent in corner
        p.push();
        p.translate(p.width - 100, 100);
        p.rotate(time * 0.2);
        p.fill(0, 212, 170, 20);
        p.noStroke();
        p.rect(-20, -20, 40, 40, 8);
        p.pop();
      };

      p.windowResized = () => {
        p.resizeCanvas(p.windowWidth, p.windowHeight);
      };
    };

    p5Instance.current = new p5(sketch);

    return () => {
      if (p5Instance.current) {
        p5Instance.current.remove();
      }
    };
  }, []);

  return <div ref={sketchRef} className="fixed inset-0 -z-10" />;
};

export default AnimatedBackground;
