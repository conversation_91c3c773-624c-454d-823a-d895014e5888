import React, { useEffect, useRef } from 'react';
import p5 from 'p5';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: p5.Color;
  alpha: number;
}

const AnimatedBackground: React.FC = () => {
  const sketchRef = useRef<HTMLDivElement>(null);
  const p5Instance = useRef<p5 | null>(null);

  useEffect(() => {
    if (!sketchRef.current) return;

    const sketch = (p: p5) => {
      let particles: Particle[] = [];
      const numParticles = 50;
      let time = 0;

      p.setup = () => {
        const canvas = p.createCanvas(p.windowWidth, p.windowHeight);
        canvas.parent(sketchRef.current!);
        canvas.style('position', 'fixed');
        canvas.style('top', '0');
        canvas.style('left', '0');
        canvas.style('z-index', '-1');
        canvas.style('pointer-events', 'none');

        // Initialize particles
        for (let i = 0; i < numParticles; i++) {
          particles.push({
            x: p.random(p.width),
            y: p.random(p.height),
            vx: p.random(-1, 1),
            vy: p.random(-1, 1),
            size: p.random(3, 8),
            color: p.color(p.random(100, 255), p.random(100, 255), p.random(200, 255)),
            alpha: p.random(0.3, 0.8)
          });
        }
      };

      p.draw = () => {
        // Create gradient background
        for (let i = 0; i <= p.height; i++) {
          const inter = p.map(i, 0, p.height, 0, 1);
          const c = p.lerpColor(
            p.color(102, 126, 234), // #667eea
            p.color(118, 75, 162),  // #764ba2
            inter
          );
          p.stroke(c);
          p.line(0, i, p.width, i);
        }

        time += 0.01;

        // Update and draw particles
        particles.forEach((particle, index) => {
          // Update position with smooth movement
          particle.x += particle.vx + Math.sin(time + index * 0.1) * 0.5;
          particle.y += particle.vy + Math.cos(time + index * 0.1) * 0.5;

          // Wrap around screen
          if (particle.x < 0) particle.x = p.width;
          if (particle.x > p.width) particle.x = 0;
          if (particle.y < 0) particle.y = p.height;
          if (particle.y > p.height) particle.y = 0;

          // Draw particle with glow effect
          p.push();
          p.translate(particle.x, particle.y);
          
          // Outer glow
          for (let r = particle.size * 3; r > 0; r--) {
            p.fill(
              p.red(particle.color),
              p.green(particle.color),
              p.blue(particle.color),
              (particle.alpha * 255) * (1 - r / (particle.size * 3)) * 0.1
            );
            p.noStroke();
            p.ellipse(0, 0, r);
          }

          // Main particle
          p.fill(
            p.red(particle.color),
            p.green(particle.color),
            p.blue(particle.color),
            particle.alpha * 255
          );
          p.noStroke();
          p.ellipse(0, 0, particle.size);
          p.pop();
        });

        // Draw connections between nearby particles
        for (let i = 0; i < particles.length; i++) {
          for (let j = i + 1; j < particles.length; j++) {
            const distance = p.dist(
              particles[i].x, particles[i].y,
              particles[j].x, particles[j].y
            );

            if (distance < 100) {
              const alpha = p.map(distance, 0, 100, 0.3, 0);
              p.stroke(255, 255, 255, alpha * 255);
              p.strokeWeight(1);
              p.line(
                particles[i].x, particles[i].y,
                particles[j].x, particles[j].y
              );
            }
          }
        }

        // Add floating geometric shapes
        p.push();
        p.translate(p.width * 0.8, p.height * 0.2);
        p.rotate(time * 0.5);
        p.fill(255, 255, 255, 30);
        p.noStroke();
        p.rect(-50, -50, 100, 100, 20);
        p.pop();

        p.push();
        p.translate(p.width * 0.1, p.height * 0.7);
        p.rotate(-time * 0.3);
        p.fill(255, 255, 255, 20);
        p.noStroke();
        p.ellipse(0, 0, 80);
        p.pop();

        p.push();
        p.translate(p.width * 0.9, p.height * 0.8);
        p.rotate(time * 0.7);
        p.fill(255, 255, 255, 25);
        p.noStroke();
        p.triangle(-30, 30, 30, 30, 0, -30);
        p.pop();
      };

      p.windowResized = () => {
        p.resizeCanvas(p.windowWidth, p.windowHeight);
      };
    };

    p5Instance.current = new p5(sketch);

    return () => {
      if (p5Instance.current) {
        p5Instance.current.remove();
      }
    };
  }, []);

  return <div ref={sketchRef} className="fixed inset-0 -z-10" />;
};

export default AnimatedBackground;
