import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const AnimatedBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const dotsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const numDots = 20;
    const dots: HTMLDivElement[] = [];

    // Create dots
    for (let i = 0; i < numDots; i++) {
      const dot = document.createElement('div');
      dot.className = 'absolute rounded-full bg-accent opacity-10';
      dot.style.width = `${Math.random() * 4 + 2}px`;
      dot.style.height = dot.style.width;
      dot.style.left = `${Math.random() * 100}%`;
      dot.style.top = `${Math.random() * 100}%`;

      container.appendChild(dot);
      dots.push(dot);
      dotsRef.current.push(dot);
    }

    // GSAP Timeline for sophisticated animations
    const tl = gsap.timeline({ repeat: -1 });

    // Animate dots with subtle movement
    dots.forEach((dot, index) => {
      // Random floating animation
      gsap.to(dot, {
        x: `+=${Math.random() * 100 - 50}`,
        y: `+=${Math.random() * 100 - 50}`,
        duration: 20 + Math.random() * 10,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.5
      });

      // Subtle opacity pulsing
      gsap.to(dot, {
        opacity: 0.05 + Math.random() * 0.15,
        duration: 3 + Math.random() * 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
        delay: index * 0.2
      });
    });

    // Create subtle geometric shapes
    const createGeometricShape = (type: 'circle' | 'square' | 'triangle', size: number) => {
      const shape = document.createElement('div');
      shape.className = 'absolute border border-accent opacity-5';

      if (type === 'circle') {
        shape.style.borderRadius = '50%';
      } else if (type === 'square') {
        shape.style.borderRadius = '8px';
      }

      shape.style.width = `${size}px`;
      shape.style.height = `${size}px`;
      shape.style.left = `${Math.random() * 100}%`;
      shape.style.top = `${Math.random() * 100}%`;

      container.appendChild(shape);

      // Subtle rotation and movement
      gsap.to(shape, {
        rotation: 360,
        duration: 60 + Math.random() * 40,
        repeat: -1,
        ease: "none"
      });

      gsap.to(shape, {
        x: `+=${Math.random() * 50 - 25}`,
        y: `+=${Math.random() * 50 - 25}`,
        duration: 30 + Math.random() * 20,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

      return shape;
    };

    // Add a few geometric shapes
    createGeometricShape('circle', 60);
    createGeometricShape('square', 40);
    createGeometricShape('circle', 80);

    // Cleanup function
    return () => {
      // Kill all GSAP animations
      gsap.killTweensOf(dotsRef.current);

      // Remove all created elements
      dotsRef.current.forEach(dot => {
        if (dot.parentNode) {
          dot.parentNode.removeChild(dot);
        }
      });

      // Clear the container
      if (container) {
        container.innerHTML = '';
      }

      dotsRef.current = [];
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 -z-10 overflow-hidden"
      style={{ background: '#0f0f0f' }}
    />
  );
};

export default AnimatedBackground;
