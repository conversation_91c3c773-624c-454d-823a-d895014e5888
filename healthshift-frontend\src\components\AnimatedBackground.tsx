import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const AnimatedBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const elementsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const elements: HTMLDivElement[] = [];

    // Create floating particles
    for (let i = 0; i < 15; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute rounded-full bg-accent opacity-5';
      const size = Math.random() * 6 + 2;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;

      container.appendChild(particle);
      elements.push(particle);
      elementsRef.current.push(particle);

      // Advanced floating animation
      gsap.to(particle, {
        x: `+=${Math.random() * 200 - 100}`,
        y: `+=${Math.random() * 200 - 100}`,
        duration: 15 + Math.random() * 10,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: i * 0.3
      });

      // Morphing opacity with breathing effect
      gsap.to(particle, {
        opacity: 0.02 + Math.random() * 0.08,
        scale: 0.5 + Math.random() * 1.5,
        duration: 4 + Math.random() * 3,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
        delay: i * 0.1
      });
    }

    // Create morphing geometric shapes
    const createMorphingShape = (type: 'circle' | 'square' | 'triangle', size: number) => {
      const shape = document.createElement('div');
      shape.className = 'absolute border border-accent opacity-3';

      if (type === 'circle') {
        shape.style.borderRadius = '50%';
      } else if (type === 'square') {
        shape.style.borderRadius = '12px';
      } else {
        shape.style.borderRadius = '0';
        shape.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';
      }

      shape.style.width = `${size}px`;
      shape.style.height = `${size}px`;
      shape.style.left = `${Math.random() * 100}%`;
      shape.style.top = `${Math.random() * 100}%`;

      container.appendChild(shape);
      elements.push(shape);

      // Complex morphing animation
      const tl = gsap.timeline({ repeat: -1 });

      tl.to(shape, {
        rotation: 360,
        scale: 0.5,
        borderRadius: type === 'circle' ? '0%' : '50%',
        duration: 8,
        ease: "power2.inOut"
      })
      .to(shape, {
        rotation: 720,
        scale: 1.5,
        borderRadius: '25%',
        duration: 8,
        ease: "power2.inOut"
      })
      .to(shape, {
        rotation: 1080,
        scale: 1,
        borderRadius: type === 'circle' ? '50%' : '12px',
        duration: 8,
        ease: "power2.inOut"
      });

      // Floating movement
      gsap.to(shape, {
        x: `+=${Math.random() * 100 - 50}`,
        y: `+=${Math.random() * 100 - 50}`,
        duration: 25 + Math.random() * 15,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: Math.random() * 5
      });

      return shape;
    };

    // Add morphing shapes
    createMorphingShape('circle', 80);
    createMorphingShape('square', 60);
    createMorphingShape('triangle', 70);
    createMorphingShape('circle', 100);

    // Create animated lines connecting elements
    const createConnectingLines = () => {
      for (let i = 0; i < 5; i++) {
        const line = document.createElement('div');
        line.className = 'absolute bg-accent opacity-2';
        line.style.height = '1px';
        line.style.width = '0px';
        line.style.left = `${Math.random() * 100}%`;
        line.style.top = `${Math.random() * 100}%`;
        line.style.transformOrigin = 'left center';

        container.appendChild(line);
        elements.push(line);

        // Animated line drawing
        gsap.to(line, {
          width: `${50 + Math.random() * 100}px`,
          duration: 2 + Math.random() * 3,
          repeat: -1,
          yoyo: true,
          ease: "power2.inOut",
          delay: i * 0.5
        });

        // Rotate the line
        gsap.to(line, {
          rotation: 360,
          duration: 20 + Math.random() * 10,
          repeat: -1,
          ease: "none"
        });
      }
    };

    createConnectingLines();

    // Create pulsing orbs
    const createPulsingOrbs = () => {
      for (let i = 0; i < 3; i++) {
        const orb = document.createElement('div');
        orb.className = 'absolute rounded-full bg-accent opacity-1';
        const size = 20 + Math.random() * 30;
        orb.style.width = `${size}px`;
        orb.style.height = `${size}px`;
        orb.style.left = `${Math.random() * 100}%`;
        orb.style.top = `${Math.random() * 100}%`;
        orb.style.filter = 'blur(10px)';

        container.appendChild(orb);
        elements.push(orb);

        // Pulsing animation
        gsap.to(orb, {
          scale: 2,
          opacity: 0.05,
          duration: 3 + Math.random() * 2,
          repeat: -1,
          yoyo: true,
          ease: "power2.inOut",
          delay: i * 1
        });

        // Floating movement
        gsap.to(orb, {
          x: `+=${Math.random() * 150 - 75}`,
          y: `+=${Math.random() * 150 - 75}`,
          duration: 20 + Math.random() * 10,
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut"
        });
      }
    };

    createPulsingOrbs();

    // Mouse interaction effect
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      const x = (clientX / window.innerWidth) * 100;
      const y = (clientY / window.innerHeight) * 100;

      // Move nearby elements towards cursor
      elements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const elementX = rect.left + rect.width / 2;
        const elementY = rect.top + rect.height / 2;
        const distance = Math.sqrt(
          Math.pow(clientX - elementX, 2) + Math.pow(clientY - elementY, 2)
        );

        if (distance < 200) {
          const force = (200 - distance) / 200;
          gsap.to(element, {
            x: `+=${(clientX - elementX) * force * 0.1}`,
            y: `+=${(clientY - elementY) * force * 0.1}`,
            duration: 0.3,
            ease: "power2.out"
          });
        }
      });
    };

    window.addEventListener('mousemove', handleMouseMove);

    // Cleanup function
    return () => {
      // Remove mouse event listener
      window.removeEventListener('mousemove', handleMouseMove);

      // Kill all GSAP animations
      gsap.killTweensOf(elementsRef.current);

      // Remove all created elements
      elementsRef.current.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });

      // Clear the container
      if (container) {
        container.innerHTML = '';
      }

      elementsRef.current = [];
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 -z-10 overflow-hidden"
      style={{
        background: 'radial-gradient(ellipse at center, #1a1a1a 0%, #0f0f0f 70%)',
        backgroundSize: '100% 100%'
      }}
    />
  );
};

export default AnimatedBackground;
