{"name": "strip-dirs", "version": "3.0.0", "description": "Remove leading directory components from a path, like tar's --strip-components option", "repository": "shinnn/node-strip-dirs", "author": "<PERSON><PERSON><PERSON> (https://github.com/shinnn)", "license": "ISC", "files": ["index.js"], "scripts": {"pretest": "eslint .", "test": "nyc node test.js"}, "keywords": ["filepath", "file-path", "path", "dir", "directory", "strip", "strip-components"], "dependencies": {"inspect-with-kind": "^1.0.5", "is-plain-obj": "^1.1.0"}, "devDependencies": {"@shinnn/eslint-config": "^6.7.0", "eslint": "^5.5.0", "nyc": "^13.0.1", "nyc-config-common": "^1.0.0", "tape": "^4.9.1"}, "eslintConfig": {"extends": "@shinnn"}, "nyc": {"extends": "nyc-config-common"}}