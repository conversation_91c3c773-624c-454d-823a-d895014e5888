import { describe, it, expect } from 'vitest';
import {
  formatCurrency,
  formatDate,
  formatDateTime,
  formatTime,
  calculateShiftDuration,
  calculateShiftEarnings,
  getInitials,
  truncateText,
  isValidEmail,
  isValidPhoneNumber,
  capitalizeFirst,
  formatSpecialization,
  getStatusColor,
  getRatingStars,
} from '../utils';

describe('Utils', () => {
  describe('formatCurrency', () => {
    it('formats currency correctly', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
      expect(formatCurrency(0)).toBe('$0.00');
      expect(formatCurrency(999999.99)).toBe('$999,999.99');
    });
  });

  describe('formatDate', () => {
    it('formats date correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      const formatted = formatDate(date);
      expect(formatted).toMatch(/January 15, 2024/);
    });

    it('handles string dates', () => {
      const formatted = formatDate('2024-01-15T10:30:00Z');
      expect(formatted).toMatch(/January 15, 2024/);
    });
  });

  describe('formatDateTime', () => {
    it('formats date and time correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      const formatted = formatDateTime(date);
      expect(formatted).toMatch(/Jan 15, 2024/);
      expect(formatted).toMatch(/10:30/);
    });
  });

  describe('formatTime', () => {
    it('formats time correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      const formatted = formatTime(date);
      expect(formatted).toMatch(/10:30/);
    });
  });

  describe('calculateShiftDuration', () => {
    it('calculates duration in hours correctly', () => {
      const start = '2024-01-15T08:00:00Z';
      const end = '2024-01-15T16:00:00Z';
      expect(calculateShiftDuration(start, end)).toBe(8);
    });

    it('handles overnight shifts', () => {
      const start = '2024-01-15T22:00:00Z';
      const end = '2024-01-16T06:00:00Z';
      expect(calculateShiftDuration(start, end)).toBe(8);
    });
  });

  describe('calculateShiftEarnings', () => {
    it('calculates earnings correctly', () => {
      expect(calculateShiftEarnings(25, 8)).toBe(200);
      expect(calculateShiftEarnings(45.5, 12)).toBe(546);
    });
  });

  describe('getInitials', () => {
    it('returns correct initials', () => {
      expect(getInitials('John', 'Doe')).toBe('JD');
      expect(getInitials('jane', 'smith')).toBe('JS');
      expect(getInitials('A', 'B')).toBe('AB');
    });
  });

  describe('truncateText', () => {
    it('truncates text when longer than max length', () => {
      const text = 'This is a very long text that should be truncated';
      expect(truncateText(text, 20)).toBe('This is a very long ...');
    });

    it('returns original text when shorter than max length', () => {
      const text = 'Short text';
      expect(truncateText(text, 20)).toBe('Short text');
    });

    it('returns original text when exactly max length', () => {
      const text = 'Exactly twenty chars';
      expect(truncateText(text, 20)).toBe('Exactly twenty chars');
    });
  });

  describe('isValidEmail', () => {
    it('validates correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@domain.com')).toBe(false);
      expect(isValidEmail('test.domain.com')).toBe(false);
      expect(isValidEmail('')).toBe(false);
    });
  });

  describe('isValidPhoneNumber', () => {
    it('validates correct phone numbers', () => {
      expect(isValidPhoneNumber('******-123-4567')).toBe(true);
      expect(isValidPhoneNumber('(*************')).toBe(true);
      expect(isValidPhoneNumber('************')).toBe(true);
      expect(isValidPhoneNumber('5551234567')).toBe(true);
    });

    it('rejects invalid phone numbers', () => {
      expect(isValidPhoneNumber('123')).toBe(false);
      expect(isValidPhoneNumber('abc-def-ghij')).toBe(false);
      expect(isValidPhoneNumber('')).toBe(false);
    });
  });

  describe('capitalizeFirst', () => {
    it('capitalizes first letter', () => {
      expect(capitalizeFirst('hello')).toBe('Hello');
      expect(capitalizeFirst('WORLD')).toBe('WORLD');
      expect(capitalizeFirst('a')).toBe('A');
      expect(capitalizeFirst('')).toBe('');
    });
  });

  describe('formatSpecialization', () => {
    it('formats specialization correctly', () => {
      expect(formatSpecialization('registered_nurse')).toBe('Registered Nurse');
      expect(formatSpecialization('licensed_practical_nurse')).toBe('Licensed Practical Nurse');
      expect(formatSpecialization('single')).toBe('Single');
    });
  });

  describe('getStatusColor', () => {
    it('returns correct colors for different statuses', () => {
      expect(getStatusColor('open')).toBe('text-green-600 bg-green-100');
      expect(getStatusColor('booked')).toBe('text-yellow-600 bg-yellow-100');
      expect(getStatusColor('completed')).toBe('text-blue-600 bg-blue-100');
      expect(getStatusColor('cancelled')).toBe('text-red-600 bg-red-100');
      expect(getStatusColor('pending')).toBe('text-yellow-600 bg-yellow-100');
      expect(getStatusColor('confirmed')).toBe('text-green-600 bg-green-100');
      expect(getStatusColor('unknown')).toBe('text-gray-600 bg-gray-100');
    });
  });

  describe('getRatingStars', () => {
    it('returns correct star representation', () => {
      expect(getRatingStars(5)).toBe('★★★★★');
      expect(getRatingStars(4)).toBe('★★★★☆');
      expect(getRatingStars(3.5)).toBe('★★★☆☆');
      expect(getRatingStars(2)).toBe('★★☆☆☆');
      expect(getRatingStars(0)).toBe('☆☆☆☆☆');
    });
  });
});
