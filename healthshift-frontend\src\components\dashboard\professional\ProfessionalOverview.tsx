import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { 
  Calendar, 
  DollarSign, 
  Clock, 
  Star, 
  TrendingUp, 
  MapPin,
  ArrowRight,
  Plus
} from 'lucide-react';
import { formatCurrency } from '../../../lib/utils';

// Mock data - in a real app, this would come from API
const mockStats = {
  totalEarnings: 12450.00,
  completedShifts: 127,
  averageRating: 4.8,
  hoursWorked: 1016,
  upcomingShifts: 3,
  pendingApplications: 2,
};

const mockUpcomingShifts = [
  {
    id: '1',
    title: 'ICU Night Shift',
    facility: 'City General Hospital',
    date: '2024-01-15',
    startTime: '19:00',
    endTime: '07:00',
    rate: 48.00,
    location: 'Springfield, IL',
  },
  {
    id: '2',
    title: 'Weekend Clinic Coverage',
    facility: 'Sunnydale Family Clinic',
    date: '2024-01-13',
    startTime: '08:00',
    endTime: '16:00',
    rate: 35.00,
    location: 'Springfield, IL',
  },
];

const mockRecentActivity = [
  {
    id: '1',
    type: 'shift_completed',
    title: 'Completed shift at City General Hospital',
    time: '2 hours ago',
    amount: 384.00,
  },
  {
    id: '2',
    type: 'application_accepted',
    title: 'Application accepted for ICU Night Shift',
    time: '1 day ago',
  },
  {
    id: '3',
    type: 'payment_received',
    title: 'Payment received for weekend shift',
    time: '2 days ago',
    amount: 280.00,
  },
];

export function ProfessionalOverview() {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="text-primary-100 mb-4">
          You have {mockStats.upcomingShifts} upcoming shifts and {mockStats.pendingApplications} pending applications.
        </p>
        <div className="flex flex-col sm:flex-row gap-3">
          <Link
            to="/professional/shifts"
            className="bg-white text-primary-600 hover:bg-gray-50 px-4 py-2 rounded-md font-medium inline-flex items-center justify-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Find New Shifts
          </Link>
          <Link
            to="/professional/bookings"
            className="bg-primary-800 hover:bg-primary-900 px-4 py-2 rounded-md font-medium inline-flex items-center justify-center"
          >
            View My Bookings
            <ArrowRight className="h-4 w-4 ml-2" />
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatCurrency(mockStats.totalEarnings)}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed Shifts</p>
              <p className="text-2xl font-semibold text-gray-900">
                {mockStats.completedShifts}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Star className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Rating</p>
              <p className="text-2xl font-semibold text-gray-900">
                {mockStats.averageRating}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Hours Worked</p>
              <p className="text-2xl font-semibold text-gray-900">
                {mockStats.hoursWorked.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Shifts */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Upcoming Shifts</h2>
            <Link
              to="/professional/bookings"
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              View all
            </Link>
          </div>
          <div className="space-y-4">
            {mockUpcomingShifts.map((shift) => (
              <div key={shift.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-gray-900">{shift.title}</h3>
                  <span className="text-lg font-semibold text-green-600">
                    {formatCurrency(shift.rate)}/hr
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{shift.facility}</p>
                <div className="flex items-center text-sm text-gray-500 space-x-4">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {new Date(shift.date).toLocaleDateString()}
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {shift.startTime} - {shift.endTime}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    {shift.location}
                  </div>
                </div>
              </div>
            ))}
            {mockUpcomingShifts.length === 0 && (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No upcoming shifts</p>
                <Link
                  to="/professional/shifts"
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  Find shifts to book
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
            <TrendingUp className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {mockRecentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-2 w-2 bg-primary-600 rounded-full mt-2"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500">{activity.time}</p>
                    {activity.amount && (
                      <span className="text-sm font-medium text-green-600">
                        +{formatCurrency(activity.amount)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            to="/professional/shifts"
            className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <Plus className="h-6 w-6 text-primary-600 mb-2" />
            <h3 className="font-medium text-gray-900">Find Shifts</h3>
            <p className="text-sm text-gray-500">Browse available opportunities</p>
          </Link>
          
          <Link
            to="/professional/profile"
            className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <Star className="h-6 w-6 text-primary-600 mb-2" />
            <h3 className="font-medium text-gray-900">Update Profile</h3>
            <p className="text-sm text-gray-500">Keep your profile current</p>
          </Link>
          
          <Link
            to="/professional/bookings"
            className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <Calendar className="h-6 w-6 text-primary-600 mb-2" />
            <h3 className="font-medium text-gray-900">My Bookings</h3>
            <p className="text-sm text-gray-500">Manage your schedule</p>
          </Link>
          
          <Link
            to="/professional/settings"
            className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <Clock className="h-6 w-6 text-primary-600 mb-2" />
            <h3 className="font-medium text-gray-900">Availability</h3>
            <p className="text-sm text-gray-500">Set your schedule</p>
          </Link>
        </div>
      </div>
    </div>
  );
}
