# HealthShift - Healthcare Marketplace Platform

HealthShift is a comprehensive healthcare marketplace platform that connects healthcare professionals with facilities needing temporary staffing. Built with modern technologies including TypeScript, React, Node.js, NestJS, and designed for AWS deployment.

## 🏗️ Architecture Overview

This project demonstrates a full-stack healthcare marketplace application with:

- **Frontend**: React with TypeScript, Tailwind CSS, and Vite
- **Backend**: NestJS with TypeScript, JWT authentication, and WebSocket support
- **Real-time Features**: Socket.IO for live notifications and updates
- **Testing**: Comprehensive unit and integration tests
- **Documentation**: OpenAPI/Swagger documentation

## 📁 Project Structure

```
healthshift/
├── healthshift-frontend/          # React frontend application
│   ├── src/
│   │   ├── components/           # Reusable UI components
│   │   │   ├── auth/            # Authentication components
│   │   │   ├── dashboard/       # Dashboard layouts and pages
│   │   │   ├── landing/         # Landing page components
│   │   │   └── notifications/   # Real-time notification components
│   │   ├── contexts/            # React contexts (Auth, Notifications)
│   │   ├── services/            # API services and WebSocket client
│   │   ├── types/               # TypeScript type definitions
│   │   ├── lib/                 # Utility functions
│   │   └── config/              # Configuration files
│   ├── public/                  # Static assets
│   └── tests/                   # Frontend tests
├── healthshift-backend/           # NestJS backend API
│   ├── src/
│   │   ├── auth/                # Authentication module
│   │   ├── shifts/              # Shift management module
│   │   ├── bookings/            # Booking management module
│   │   ├── notifications/       # Real-time notifications module
│   │   ├── common/              # Shared DTOs and utilities
│   │   └── main.ts              # Application entry point
│   ├── test/                    # E2E tests
│   └── dist/                    # Compiled JavaScript
└── README.md                     # This file
```

## 🚀 Features

### For Healthcare Professionals
- **Profile Management**: Complete professional profiles with credentials and certifications
- **Shift Discovery**: Browse and filter available shifts by specialization, location, and rate
- **Real-time Notifications**: Instant updates on shift confirmations and new opportunities
- **Booking Management**: Track application status and manage upcoming shifts
- **Earnings Dashboard**: Monitor total earnings and completed shifts

### For Healthcare Facilities
- **Shift Posting**: Create detailed shift postings with requirements and rates
- **Application Management**: Review and approve professional applications
- **Real-time Updates**: Live notifications for new applications and shift updates
- **Facility Profile**: Showcase facility information and build reputation
- **Analytics Dashboard**: Track fill rates and staffing metrics

### Technical Features
- **Real-time Communication**: WebSocket-based notifications and updates
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Type Safety**: Full TypeScript implementation across frontend and backend
- **Authentication**: JWT-based authentication with role-based access control
- **API Documentation**: Comprehensive Swagger/OpenAPI documentation
- **Testing**: Unit tests, integration tests, and E2E tests
- **Modern Development**: Hot reload, ESLint, Prettier, and modern tooling

## 🛠️ Technology Stack

### Frontend
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe JavaScript development
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **TanStack Query** - Server state management
- **Socket.IO Client** - Real-time communication
- **Lucide React** - Modern icon library
- **Vitest** - Fast unit testing framework

### Backend
- **NestJS** - Progressive Node.js framework
- **TypeScript** - Type-safe server development
- **JWT** - JSON Web Token authentication
- **Socket.IO** - Real-time WebSocket communication
- **Swagger/OpenAPI** - API documentation
- **Jest** - Testing framework
- **Class Validator** - Request validation
- **Passport** - Authentication middleware

## 📋 Prerequisites

- Node.js 18+ and npm
- Git for version control
- Modern web browser

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd healthshift
```

### 2. Backend Setup

```bash
cd healthshift-backend

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Start development server
npm run start:dev
```

The backend API will be available at `http://localhost:3001`
API documentation: `http://localhost:3001/api/docs`

### 3. Frontend Setup

```bash
cd healthshift-frontend

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Start development server
npm run dev
```

The frontend will be available at `http://localhost:5173`

## 🧪 Testing

### Backend Tests

```bash
cd healthshift-backend

# Run unit tests
npm test

# Run E2E tests
npm run test:e2e

# Run tests with coverage
npm run test:cov
```

### Frontend Tests

```bash
cd healthshift-frontend

# Run unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📚 API Documentation

The backend provides comprehensive API documentation using Swagger/OpenAPI:

- **Local Development**: `http://localhost:3001/api/docs`
- **Authentication**: JWT Bearer token required for protected endpoints
- **Rate Limiting**: Implemented for production use
- **Validation**: Request/response validation with detailed error messages

### Key API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User authentication
- `GET /api/shifts` - List available shifts
- `POST /api/shifts` - Create new shift (facilities only)
- `GET /api/bookings` - List bookings
- `POST /api/bookings` - Create booking (professionals only)
- `GET /api/notifications` - Get user notifications

## 🔐 Authentication & Authorization

The application implements role-based access control:

- **Healthcare Professionals**: Can browse shifts, create bookings, manage profile
- **Healthcare Facilities**: Can post shifts, manage applications, view analytics
- **JWT Tokens**: Secure authentication with configurable expiration
- **Protected Routes**: Frontend route protection based on user roles

## 🔄 Real-time Features

WebSocket implementation provides:

- **Live Notifications**: Instant updates for bookings, confirmations, cancellations
- **Shift Updates**: Real-time status changes and new shift alerts
- **Connection Management**: Automatic reconnection and connection status indicators
- **Room-based Communication**: Targeted notifications by user role and specific entities

## 🎨 UI/UX Design

- **Modern Interface**: Clean, professional design suitable for healthcare
- **Responsive Layout**: Mobile-first approach with desktop optimization
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation
- **Loading States**: Skeleton screens and loading indicators
- **Error Handling**: User-friendly error messages and recovery options

## 🚀 Deployment

### Environment Variables

#### Backend (.env)
```env
DATABASE_URL="postgresql://username:password@localhost:5432/healthshift"
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="7d"
PORT=3001
NODE_ENV="production"
FRONTEND_URL="https://your-frontend-domain.com"
```

#### Frontend (.env)
```env
VITE_API_BASE_URL="https://your-api-domain.com/api"
VITE_WS_URL="https://your-api-domain.com"
```

### AWS Deployment Ready

The application is designed for AWS deployment with:

- **Frontend**: S3 + CloudFront for static hosting
- **Backend**: ECS/Fargate or EC2 for API hosting
- **Database**: RDS PostgreSQL for production data
- **WebSockets**: ALB with sticky sessions for Socket.IO
- **Monitoring**: CloudWatch integration ready

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built to demonstrate modern full-stack development practices
- Designed for Clipboard Health's tech stack requirements
- Implements healthcare industry best practices for staffing platforms

---

**Note**: This is a demonstration project showcasing modern web development practices for healthcare marketplace applications. For production use, additional security measures, database integration, and compliance features would be required.
