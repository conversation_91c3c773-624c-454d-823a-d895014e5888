import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
}

@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    credentials: true,
  },
  namespace: '/notifications',
})
export class NotificationsGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(NotificationsGateway.name);
  private connectedUsers = new Map<string, string>(); // userId -> socketId

  constructor(private jwtService: JwtService) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Client ${client.id} attempted to connect without token`);
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      client.userRole = payload.role;

      // Validate that we have required user information
      if (!client.userId || !client.userRole) {
        this.logger.warn(`Client ${client.id} has invalid token payload`);
        client.disconnect();
        return;
      }

      // Store the connection
      this.connectedUsers.set(client.userId, client.id);

      // Join user-specific room
      client.join(`user:${client.userId}`);
      
      // Join role-specific room
      client.join(`role:${client.userRole}`);

      this.logger.log(`User ${client.userId} (${client.userRole}) connected with socket ${client.id}`);

      // Send connection confirmation
      client.emit('connected', {
        message: 'Successfully connected to notifications',
        userId: client.userId,
      });

    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error.message);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      this.connectedUsers.delete(client.userId);
      this.logger.log(`User ${client.userId} disconnected`);
    }
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room: string },
  ) {
    if (!client.userId) {
      client.emit('error', { message: 'User not authenticated' });
      return;
    }

    client.join(data.room);
    this.logger.log(`User ${client.userId} joined room: ${data.room}`);
    client.emit('joined-room', { room: data.room });
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room: string },
  ) {
    if (!client.userId) {
      client.emit('error', { message: 'User not authenticated' });
      return;
    }

    client.leave(data.room);
    this.logger.log(`User ${client.userId} left room: ${data.room}`);
    client.emit('left-room', { room: data.room });
  }

  // Method to send notification to a specific user
  sendNotificationToUser(userId: string, notification: any) {
    this.server.to(`user:${userId}`).emit('notification', notification);
    this.logger.log(`Sent notification to user ${userId}:`, notification.type);
  }

  // Method to send notification to all users with a specific role
  sendNotificationToRole(role: string, notification: any) {
    this.server.to(`role:${role}`).emit('notification', notification);
    this.logger.log(`Sent notification to role ${role}:`, notification.type);
  }

  // Method to send notification to all connected users
  sendBroadcastNotification(notification: any) {
    this.server.emit('notification', notification);
    this.logger.log(`Broadcast notification:`, notification.type);
  }

  // Method to send shift update notifications
  sendShiftUpdate(shiftId: string, update: any) {
    this.server.to(`shift:${shiftId}`).emit('shift-update', update);
    this.logger.log(`Sent shift update for shift ${shiftId}:`, update.type);
  }

  // Method to send booking update notifications
  sendBookingUpdate(bookingId: string, update: any) {
    this.server.to(`booking:${bookingId}`).emit('booking-update', update);
    this.logger.log(`Sent booking update for booking ${bookingId}:`, update.type);
  }

  // Get connected users count
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  // Check if user is online
  isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }
}
