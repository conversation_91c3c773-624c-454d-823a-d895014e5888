{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/discriminator/index.ts"], "names": [], "mappings": ";;AAEA,mDAA0D;AAC1D,kDAAgE;AAChE,2CAAmD;AACnD,uDAAqD;AACrD,6CAAuD;AAIvD,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,UAAU,EAAE,OAAO,EAAC,EAAC,EAAE,EAAE,CAC3C,UAAU,KAAK,kBAAU,CAAC,GAAG;QAC3B,CAAC,CAAC,QAAQ,OAAO,kBAAkB;QACnC,CAAC,CAAC,iBAAiB,OAAO,oBAAoB;IAClD,MAAM,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,UAAU,EAAE,GAAG,EAAE,OAAO,EAAC,EAAC,EAAE,EAAE,CAC/C,IAAA,WAAC,EAAA,WAAW,UAAU,UAAU,OAAO,eAAe,GAAG,GAAG;CAC/D,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,eAAe;IACxB,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,QAAQ;IACpB,KAAK;IACL,IAAI,CAAC,GAAe;QAClB,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QACjD,MAAM,EAAC,KAAK,EAAC,GAAG,YAAY,CAAA;QAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;QACjE,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,CAAC,YAAY,CAAA;QACnC,IAAI,OAAO,OAAO,IAAI,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;QACvF,IAAI,MAAM,CAAC,OAAO;YAAE,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;QAC9E,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;QACpE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QACrC,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,GAAG,IAAA,qBAAW,EAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QAC/D,GAAG,CAAC,EAAE,CACJ,IAAA,WAAC,EAAA,UAAU,GAAG,cAAc,EAC5B,GAAG,EAAE,CAAC,eAAe,EAAE,EACvB,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAC,UAAU,EAAE,kBAAU,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAC,CAAC,CACnE,CAAA;QACD,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QAEb,SAAS,eAAe;YACtB,MAAM,OAAO,GAAG,UAAU,EAAE,CAAA;YAC5B,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;YACb,KAAK,MAAM,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAC/B,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,QAAQ,QAAQ,EAAE,CAAC,CAAA;gBACrC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;YACtD,CAAC;YACD,GAAG,CAAC,IAAI,EAAE,CAAA;YACV,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAC,UAAU,EAAE,kBAAU,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAC,CAAC,CAAA;YAChE,GAAG,CAAC,KAAK,EAAE,CAAA;QACb,CAAC;QAED,SAAS,cAAc,CAAC,UAAmB;YACzC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAChC,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAC,EAAE,MAAM,CAAC,CAAA;YACpE,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,cAAI,CAAC,CAAA;YAChC,OAAO,MAAM,CAAA;QACf,CAAC;QAED,SAAS,UAAU;;YACjB,MAAM,YAAY,GAA6B,EAAE,CAAA;YACjD,MAAM,WAAW,GAAG,WAAW,CAAC,YAAY,CAAC,CAAA;YAC7C,IAAI,WAAW,GAAG,IAAI,CAAA;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;gBAClB,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,KAAI,CAAC,IAAA,2BAAoB,EAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC3D,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAA;oBACpB,GAAG,GAAG,oBAAU,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;oBACjE,IAAI,GAAG,YAAY,mBAAS;wBAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAA;oBAC9C,IAAI,GAAG,KAAK,SAAS;wBAAE,MAAM,IAAI,mBAAe,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;gBACvF,CAAC;gBACD,MAAM,OAAO,GAAG,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,UAAU,0CAAG,OAAO,CAAC,CAAA;gBAC1C,IAAI,OAAO,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,MAAM,IAAI,KAAK,CACb,iFAAiF,OAAO,GAAG,CAC5F,CAAA;gBACH,CAAC;gBACD,WAAW,GAAG,WAAW,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC9D,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YACzB,CAAC;YACD,IAAI,CAAC,WAAW;gBAAE,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,oBAAoB,CAAC,CAAA;YACjF,OAAO,YAAY,CAAA;YAEnB,SAAS,WAAW,CAAC,EAAC,QAAQ,EAAkB;gBAC9C,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YAC9D,CAAC;YAED,SAAS,WAAW,CAAC,GAAoB,EAAE,CAAS;gBAClD,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;oBACd,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;gBAC1B,CAAC;qBAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;oBACpB,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;wBAChC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;oBACzB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,OAAO,+BAA+B,CAAC,CAAA;gBACvF,CAAC;YACH,CAAC;YAED,SAAS,UAAU,CAAC,QAAiB,EAAE,CAAS;gBAC9C,IAAI,OAAO,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY,EAAE,CAAC;oBAC5D,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,iCAAiC,CAAC,CAAA;gBAC9E,CAAC;gBACD,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}