import { supabase } from '../lib/supabase';
import { Shift, Booking, Notification, ShiftFilters } from '../types';

export class SupabaseService {
  // Shifts
  async getShifts(filters?: ShiftFilters) {
    let query = supabase
      .from('sample_shifts')
      .select('*')
      .eq('status', 'open')
      .order('start_date_time', { ascending: true });

    if (filters?.specialization) {
      query = query.eq('specialization', filters.specialization);
    }

    if (filters?.startDate) {
      query = query.gte('start_date_time', filters.startDate);
    }

    if (filters?.endDate) {
      query = query.lte('end_date_time', filters.endDate);
    }

    if (filters?.minRate) {
      query = query.gte('hourly_rate', filters.minRate);
    }

    if (filters?.maxRate) {
      query = query.lte('hourly_rate', filters.maxRate);
    }

    if (filters?.facilityType) {
      query = query.eq('facility_type', filters.facilityType);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data;
  }

  async getShiftById(id: string) {
    const { data, error } = await supabase
      .from('sample_shifts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  async createShift(shiftData: Partial<Shift>) {
    // Get the user's facility ID
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data: facility } = await supabase
      .from('facilities')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (!facility) throw new Error('Facility not found');

    const { data, error } = await supabase
      .from('shifts')
      .insert({
        facility_id: facility.id,
        title: shiftData.title,
        description: shiftData.description,
        specialization: shiftData.specialization,
        start_date_time: shiftData.startDateTime,
        end_date_time: shiftData.endDateTime,
        hourly_rate: shiftData.hourlyRate,
        requirements: shiftData.requirements || [],
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateShift(id: string, updates: Partial<Shift>) {
    const { data, error } = await supabase
      .from('shifts')
      .update({
        title: updates.title,
        description: updates.description,
        specialization: updates.specialization,
        start_date_time: updates.startDateTime,
        end_date_time: updates.endDateTime,
        hourly_rate: updates.hourlyRate,
        requirements: updates.requirements,
        status: updates.status,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteShift(id: string) {
    const { error } = await supabase
      .from('shifts')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Bookings
  async createBooking(shiftId: string, notes?: string) {
    // Get the user's professional ID
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data: professional } = await supabase
      .from('healthcare_professionals')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (!professional) throw new Error('Professional profile not found');

    const { data, error } = await supabase
      .from('bookings')
      .insert({
        shift_id: shiftId,
        professional_id: professional.id,
        notes: notes || null,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getBookings() {
    const { data, error } = await supabase
      .from('bookings_with_details')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async updateBookingStatus(id: string, status: string) {
    const { data, error } = await supabase
      .from('bookings')
      .update({ status })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Notifications
  async getNotifications() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async markNotificationAsRead(id: string) {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', id);

    if (error) throw error;
  }

  async markAllNotificationsAsRead() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('user_id', user.id)
      .eq('read', false);

    if (error) throw error;
  }

  async getUnreadNotificationCount() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('read', false);

    if (error) throw error;
    return count || 0;
  }

  // Real-time subscriptions
  subscribeToShifts(callback: (payload: any) => void) {
    return supabase
      .channel('shifts')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'shifts' }, callback)
      .subscribe();
  }

  subscribeToBookings(callback: (payload: any) => void) {
    return supabase
      .channel('bookings')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'bookings' }, callback)
      .subscribe();
  }

  subscribeToNotifications(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('notifications')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'notifications', filter: `user_id=eq.${userId}` }, 
        callback
      )
      .subscribe();
  }
}

export const supabaseService = new SupabaseService();
