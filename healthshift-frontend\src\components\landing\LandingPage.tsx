import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Heart,
  Users,
  Clock,
  Shield,
  Star,
  ArrowRight,
  CheckCircle,
  Building,
  User,
  Zap,
  Award,
  TrendingUp
} from 'lucide-react';
import AnimatedBackground from '../AnimatedBackground';
import AnimatedText from '../AnimatedText';
import FloatingActionButton from '../FloatingActionButton';
import LoadingAnimation from '../LoadingAnimation';
import { useGSAPAnimations, useCounterAnimation } from '../../hooks/useGSAPAnimations';

function LandingPage() {
  const [showLoading, setShowLoading] = useState(false); // Disabled for now
  const containerRef = useGSAPAnimations();
  const professionalsCountRef = useCounterAnimation(10000);
  const facilitiesCountRef = useCounterAnimation(500);
  const shiftsCountRef = useCounterAnimation(50000);

  return (
    <>
      {showLoading && (
        <LoadingAnimation
          isVisible={showLoading}
          onComplete={() => setShowLoading(false)}
        />
      )}

      <div ref={containerRef} className="min-h-screen relative overflow-hidden">
        <AnimatedBackground />

      {/* Header */}
      <header className="cred-glass relative z-10 border-b border-cred-border">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center animate-fade-in">
              <div className="icon-container-secondary w-10 h-10 mr-3">
                <Heart className="h-5 w-5" />
              </div>
              <span className="text-2xl font-bold text-white">HealthShift</span>
            </div>
            <div className="flex items-center space-x-6">
              <Link
                to="/login"
                className="btn-ghost"
              >
                Sign In
              </Link>
              <Link
                to="/register"
                className="btn-primary"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="mb-8">
              <span className="hero-badge inline-flex items-center bg-card border border-cred-border text-accent px-4 py-2 rounded-full text-sm font-medium">
                <Zap className="h-4 w-4 mr-2" />
                #1 Healthcare Marketplace
              </span>
            </div>
            <h1 className="hero-title text-hero text-white mb-8 max-w-4xl mx-auto text-center">
              <AnimatedText
                text="Connect Healthcare"
                className="block"
                animationType="wave"
                delay={0.5}
              />
              <span className="text-gradient block">
                <AnimatedText
                  text="Professionals"
                  animationType="glitch"
                  delay={1.2}
                />
              </span>
              <AnimatedText
                text="with Opportunities"
                className="block"
                animationType="reveal"
                delay={1.8}
              />
            </h1>
            <p className="hero-subtitle text-body-large max-w-2xl mx-auto mb-12 text-center">
              <AnimatedText
                text="The leading marketplace for healthcare staffing. Find shifts, build your career, and make a difference in healthcare."
                animationType="typewriter"
                delay={2.5}
              />
            </p>
            <div className="hero-buttons flex flex-col sm:flex-row gap-4 justify-center mb-20">
              <Link
                to="/register"
                className="btn-primary text-lg px-8 py-4"
              >
                <User className="h-5 w-5 mr-3" />
                I'm a Healthcare Professional
              </Link>
              <Link
                to="/register"
                className="btn-secondary text-lg px-8 py-4"
              >
                <Building className="h-5 w-5 mr-3" />
                I'm a Healthcare Facility
              </Link>
            </div>

            {/* Stats */}
            <div className="hero-stats grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="stat-card">
                <div className="text-3xl font-bold text-accent mb-2">
                  <span ref={professionalsCountRef}>10,000</span>+
                </div>
                <div className="text-caption">Healthcare Professionals</div>
              </div>
              <div className="stat-card">
                <div className="text-3xl font-bold text-accent mb-2">
                  <span ref={facilitiesCountRef}>500</span>+
                </div>
                <div className="text-caption">Healthcare Facilities</div>
              </div>
              <div className="stat-card">
                <div className="text-3xl font-bold text-accent mb-2">
                  <span ref={shiftsCountRef}>50,000</span>+
                </div>
                <div className="text-caption">Shifts Completed</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section py-20 lg:py-32">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-display text-white mb-6">
              Why Choose HealthShift?
            </h2>
            <p className="text-body-large max-w-2xl mx-auto">
              We're revolutionizing healthcare staffing with technology that works for everyone.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="feature-card">
              <div className="icon-container mb-6">
                <Clock className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Flexible Scheduling</h3>
              <p className="text-body">
                Work when you want, where you want. Take control of your schedule and work-life balance.
              </p>
            </div>

            <div className="feature-card">
              <div className="icon-container mb-6">
                <Shield className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Secure & Trusted</h3>
              <p className="text-body">
                All professionals are verified and background-checked. Secure payments guaranteed.
              </p>
            </div>

            <div className="feature-card">
              <div className="icon-container mb-6">
                <TrendingUp className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Growing Network</h3>
              <p className="text-body">
                Join thousands of healthcare professionals already using HealthShift.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="how-it-works-section py-20 lg:py-32">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-display text-white mb-6">
              How It Works
            </h2>
            <p className="text-body-large max-w-2xl mx-auto">
              Simple, fast, and effective. Get started in minutes.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* For Professionals */}
            <div className="how-it-works-card cred-card">
              <div className="text-center mb-8">
                <div className="icon-container mx-auto mb-4">
                  <User className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  For Healthcare Professionals
                </h3>
              </div>
              <div className="space-y-cred">
                <div className="flex items-start">
                  <div className="bg-accent text-black w-8 h-8 rounded-lg flex items-center justify-center font-bold mr-4 text-sm">
                    1
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">Create Your Profile</h4>
                    <p className="text-body">Set up your professional profile with credentials and availability.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-accent text-black w-8 h-8 rounded-lg flex items-center justify-center font-bold mr-4 text-sm">
                    2
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">Browse & Apply</h4>
                    <p className="text-body">Find shifts that match your skills and schedule preferences.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-accent text-black w-8 h-8 rounded-lg flex items-center justify-center font-bold mr-4 text-sm">
                    3
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">Work & Get Paid</h4>
                    <p className="text-body">Complete your shifts and receive secure, timely payments.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* For Facilities */}
            <div className="how-it-works-card cred-card">
              <div className="text-center mb-8">
                <div className="icon-container mx-auto mb-4">
                  <Building className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  For Healthcare Facilities
                </h3>
              </div>
              <div className="space-y-cred">
                <div className="flex items-start">
                  <div className="bg-accent text-black w-8 h-8 rounded-lg flex items-center justify-center font-bold mr-4 text-sm">
                    1
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">Post Your Needs</h4>
                    <p className="text-body">Create shift postings with specific requirements and rates.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-accent text-black w-8 h-8 rounded-lg flex items-center justify-center font-bold mr-4 text-sm">
                    2
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">Review Applications</h4>
                    <p className="text-body">Browse qualified professionals and select the best fit.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-accent text-black w-8 h-8 rounded-lg flex items-center justify-center font-bold mr-4 text-sm">
                    3
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-1">Manage Shifts</h4>
                    <p className="text-body">Track attendance and manage your staffing needs efficiently.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section py-20 lg:py-32">
        <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
          <div className="cta-card card-premium p-12 rounded-2xl cred-glow">
            <h2 className="text-display text-white mb-6">
              Ready to Transform Your
              <span className="text-gradient block">Healthcare Career?</span>
            </h2>
            <p className="text-body-large mb-8 max-w-2xl mx-auto">
              Join thousands of healthcare professionals who have already discovered the flexibility and opportunities HealthShift provides.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="btn-primary text-lg px-8 py-4"
              >
                Get Started Today
                <ArrowRight className="h-5 w-5 ml-2" />
              </Link>
              <Link
                to="/login"
                className="btn-secondary text-lg px-8 py-4"
              >
                Sign In
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-cred-border py-16">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-6">
                <div className="icon-container-secondary w-8 h-8 mr-3">
                  <Heart className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-white">HealthShift</span>
              </div>
              <p className="text-body mb-6">
                Revolutionizing healthcare staffing with modern technology.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-4 text-white">For Professionals</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Find Shifts</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">How It Works</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Success Stories</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Support</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4 text-white">For Facilities</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Post Shifts</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Pricing</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Case Studies</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Enterprise</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4 text-white">Support</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Help Center</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-caption hover:text-accent transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-cred-border mt-12 pt-8 text-center">
            <p className="text-caption">
              &copy; 2024 HealthShift. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Floating Action Button */}
      <FloatingActionButton />
    </div>
    </>
  );
}

export default LandingPage;
