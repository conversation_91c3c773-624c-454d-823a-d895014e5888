import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Heart,
  Users,
  Clock,
  Shield,
  Star,
  ArrowRight,
  CheckCircle,
  Building,
  User,
  Zap,
  Award,
  TrendingUp
} from 'lucide-react';
import AnimatedBackground from '../AnimatedBackground';

function LandingPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      <AnimatedBackground />

      {/* Header */}
      <header className="glass relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center animate-fade-in">
              <div className="icon-container w-12 h-12 mr-3">
                <Heart className="h-6 w-6" />
              </div>
              <span className="text-2xl font-bold text-white">HealthShift</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-white hover:text-yellow-400 font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                to="/register"
                className="btn-orange hover-lift"
              >
                Get Started
                <ArrowRight className="h-5 w-5 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center animate-slide-up">
            <div className="mb-8">
              <span className="inline-block bg-gradient-warning text-white px-6 py-2 rounded-full text-sm font-semibold mb-4 animate-bounce">
                🚀 #1 Healthcare Marketplace
              </span>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight">
              Connect Healthcare
              <span className="text-gradient-secondary block">Professionals</span>
              with Opportunities
            </h1>
            <p className="text-xl md:text-2xl mb-12 text-white max-w-4xl mx-auto opacity-90">
              The leading marketplace for healthcare staffing. Find shifts, build your career,
              and make a difference in healthcare with our modern platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Link
                to="/register"
                className="btn-success text-lg px-8 py-4 hover-lift animate-float"
              >
                <User className="h-6 w-6 mr-3" />
                I'm a Healthcare Professional
                <ArrowRight className="h-6 w-6 ml-3" />
              </Link>
              <Link
                to="/register"
                className="btn-secondary text-lg px-8 py-4 hover-lift"
              >
                <Building className="h-6 w-6 mr-3" />
                I'm a Healthcare Facility
                <ArrowRight className="h-6 w-6 ml-3" />
              </Link>
            </div>

            {/* Floating stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
              <div className="stat-card hover-scale">
                <div className="text-3xl font-bold text-gradient mb-2">10,000+</div>
                <div className="text-white opacity-80">Healthcare Professionals</div>
              </div>
              <div className="stat-card hover-scale">
                <div className="text-3xl font-bold text-gradient mb-2">500+</div>
                <div className="text-white opacity-80">Healthcare Facilities</div>
              </div>
              <div className="stat-card hover-scale">
                <div className="text-3xl font-bold text-gradient mb-2">50,000+</div>
                <div className="text-white opacity-80">Shifts Completed</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20 animate-fade-in">
            <span className="inline-block bg-gradient-secondary text-white px-6 py-2 rounded-full text-sm font-semibold mb-6">
              ✨ Why Choose HealthShift?
            </span>
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              We're <span className="text-gradient-secondary">revolutionizing</span> healthcare staffing
            </h2>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              With technology that works for everyone, creating seamless connections between healthcare professionals and facilities.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="feature-card animate-slide-up">
              <div className="icon-container bg-gradient-success mb-6">
                <Clock className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Flexible Scheduling</h3>
              <p className="text-gray-600 text-lg leading-relaxed">
                Work when you want, where you want. Take control of your schedule and work-life balance with our smart matching system.
              </p>
            </div>

            <div className="feature-card animate-slide-up" style={{animationDelay: '0.2s'}}>
              <div className="icon-container bg-gradient-warning mb-6">
                <Shield className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Secure & Trusted</h3>
              <p className="text-gray-600 text-lg leading-relaxed">
                All professionals are verified and background-checked. Secure payments guaranteed with our advanced security protocols.
              </p>
            </div>

            <div className="feature-card animate-slide-up" style={{animationDelay: '0.4s'}}>
              <div className="icon-container bg-gradient-orange mb-6">
                <TrendingUp className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Growing Network</h3>
              <p className="text-gray-600 text-lg leading-relaxed">
                Join thousands of healthcare professionals and facilities already using HealthShift to transform their careers.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-24 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20">
            <span className="inline-block bg-gradient-primary text-white px-6 py-2 rounded-full text-sm font-semibold mb-6">
              🎯 How It Works
            </span>
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Simple. Fast. <span className="text-gradient-secondary">Effective.</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            {/* For Professionals */}
            <div className="card-gradient hover-lift">
              <div className="text-center mb-8">
                <div className="icon-container bg-gradient-success w-16 h-16 mx-auto mb-4">
                  <User className="h-8 w-8" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900">
                  For Healthcare Professionals
                </h3>
              </div>
              <div className="space-y-8">
                <div className="flex items-start">
                  <div className="bg-gradient-success text-white w-12 h-12 rounded-full flex items-center justify-center font-bold mr-6 text-lg">
                    1
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Create Your Profile</h4>
                    <p className="text-gray-600 text-lg">Set up your professional profile with credentials and availability.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-gradient-success text-white w-12 h-12 rounded-full flex items-center justify-center font-bold mr-6 text-lg">
                    2
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Browse & Apply</h4>
                    <p className="text-gray-600 text-lg">Find shifts that match your skills and schedule preferences.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-gradient-success text-white w-12 h-12 rounded-full flex items-center justify-center font-bold mr-6 text-lg">
                    3
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Work & Get Paid</h4>
                    <p className="text-gray-600 text-lg">Complete your shifts and receive secure, timely payments.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* For Facilities */}
            <div className="card-gradient hover-lift">
              <div className="text-center mb-8">
                <div className="icon-container bg-gradient-secondary w-16 h-16 mx-auto mb-4">
                  <Building className="h-8 w-8" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900">
                  For Healthcare Facilities
                </h3>
              </div>
              <div className="space-y-8">
                <div className="flex items-start">
                  <div className="bg-gradient-secondary text-white w-12 h-12 rounded-full flex items-center justify-center font-bold mr-6 text-lg">
                    1
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Post Your Needs</h4>
                    <p className="text-gray-600 text-lg">Create shift postings with specific requirements and rates.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-gradient-secondary text-white w-12 h-12 rounded-full flex items-center justify-center font-bold mr-6 text-lg">
                    2
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Review Applications</h4>
                    <p className="text-gray-600 text-lg">Browse qualified professionals and select the best fit.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-gradient-secondary text-white w-12 h-12 rounded-full flex items-center justify-center font-bold mr-6 text-lg">
                    3
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Manage Shifts</h4>
                    <p className="text-gray-600 text-lg">Track attendance and manage your staffing needs efficiently.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <div className="card-gradient p-16 rounded-3xl">
            <h2 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
              Ready to Transform Your
              <span className="text-gradient block">Healthcare Career?</span>
            </h2>
            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
              Join thousands of healthcare professionals who have already discovered the flexibility and opportunities HealthShift provides.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/register"
                className="btn-primary text-xl px-12 py-6 hover-lift animate-pulse"
              >
                Get Started Today
                <ArrowRight className="h-6 w-6 ml-3" />
              </Link>
              <Link
                to="/login"
                className="btn-secondary text-xl px-12 py-6 hover-lift"
              >
                Sign In
                <Zap className="h-6 w-6 ml-3" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="glass-dark text-white py-16 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center mb-6">
                <div className="icon-container w-12 h-12 mr-3">
                  <Heart className="h-6 w-6" />
                </div>
                <span className="text-2xl font-bold">HealthShift</span>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Revolutionizing healthcare staffing with modern technology and seamless connections.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center hover-scale cursor-pointer">
                  <span className="text-sm font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-gradient-secondary rounded-full flex items-center justify-center hover-scale cursor-pointer">
                  <span className="text-sm font-bold">t</span>
                </div>
                <div className="w-10 h-10 bg-gradient-success rounded-full flex items-center justify-center hover-scale cursor-pointer">
                  <span className="text-sm font-bold">in</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-6 text-white">For Professionals</h3>
              <ul className="space-y-3 text-gray-300">
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Find Shifts</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">How It Works</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Success Stories</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Support</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-6 text-white">For Facilities</h3>
              <ul className="space-y-3 text-gray-300">
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Post Shifts</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Case Studies</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Enterprise</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-6 text-white">Support</h3>
              <ul className="space-y-3 text-gray-300">
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-white border-opacity-20 mt-12 pt-8 text-center">
            <p className="text-gray-300 text-lg">
              &copy; 2024 HealthShift. All rights reserved. Made with ❤️ for healthcare heroes.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default LandingPage;
