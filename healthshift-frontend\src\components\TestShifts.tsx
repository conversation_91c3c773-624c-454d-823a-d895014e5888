import React, { useEffect, useState } from 'react';
import { supabaseService } from '../services/supabase';

export function TestShifts() {
  const [shifts, setShifts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadShifts();
  }, []);

  const loadShifts = async () => {
    try {
      setLoading(true);
      const data = await supabaseService.getShifts();
      setShifts(data || []);
      console.log('✅ Shifts loaded:', data);
    } catch (err: any) {
      setError(err.message);
      console.error('❌ Failed to load shifts:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h3 className="text-red-800 font-medium">Error loading shifts</h3>
          <p className="text-red-600 text-sm mt-1">{error}</p>
          <button 
            onClick={loadShifts}
            className="mt-3 bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Shifts</h2>
      
      {shifts.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No shifts available</p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {shifts.map((shift) => (
            <div key={shift.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{shift.title}</h3>
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                  {shift.status}
                </span>
              </div>
              
              <p className="text-gray-600 text-sm mb-4">{shift.description}</p>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Facility:</span>
                  <span className="font-medium">{shift.facility_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Type:</span>
                  <span className="font-medium capitalize">{shift.facility_type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Specialization:</span>
                  <span className="font-medium capitalize">{shift.specialization.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Rate:</span>
                  <span className="font-medium text-green-600">${shift.hourly_rate}/hr</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Start:</span>
                  <span className="font-medium">
                    {new Date(shift.start_date_time).toLocaleDateString()} {new Date(shift.start_date_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">End:</span>
                  <span className="font-medium">
                    {new Date(shift.end_date_time).toLocaleDateString()} {new Date(shift.end_date_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                  </span>
                </div>
              </div>
              
              <button className="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                Apply for Shift
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
