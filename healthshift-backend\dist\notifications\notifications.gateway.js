"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationsGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
let NotificationsGateway = NotificationsGateway_1 = class NotificationsGateway {
    jwtService;
    server;
    logger = new common_1.Logger(NotificationsGateway_1.name);
    connectedUsers = new Map();
    constructor(jwtService) {
        this.jwtService = jwtService;
    }
    async handleConnection(client) {
        try {
            const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
            if (!token) {
                this.logger.warn(`Client ${client.id} attempted to connect without token`);
                client.disconnect();
                return;
            }
            const payload = this.jwtService.verify(token);
            client.userId = payload.sub;
            client.userRole = payload.role;
            if (!client.userId || !client.userRole) {
                this.logger.warn(`Client ${client.id} has invalid token payload`);
                client.disconnect();
                return;
            }
            this.connectedUsers.set(client.userId, client.id);
            client.join(`user:${client.userId}`);
            client.join(`role:${client.userRole}`);
            this.logger.log(`User ${client.userId} (${client.userRole}) connected with socket ${client.id}`);
            client.emit('connected', {
                message: 'Successfully connected to notifications',
                userId: client.userId,
            });
        }
        catch (error) {
            this.logger.error(`Authentication failed for client ${client.id}:`, error.message);
            client.disconnect();
        }
    }
    handleDisconnect(client) {
        if (client.userId) {
            this.connectedUsers.delete(client.userId);
            this.logger.log(`User ${client.userId} disconnected`);
        }
    }
    handleJoinRoom(client, data) {
        if (!client.userId) {
            client.emit('error', { message: 'User not authenticated' });
            return;
        }
        client.join(data.room);
        this.logger.log(`User ${client.userId} joined room: ${data.room}`);
        client.emit('joined-room', { room: data.room });
    }
    handleLeaveRoom(client, data) {
        if (!client.userId) {
            client.emit('error', { message: 'User not authenticated' });
            return;
        }
        client.leave(data.room);
        this.logger.log(`User ${client.userId} left room: ${data.room}`);
        client.emit('left-room', { room: data.room });
    }
    sendNotificationToUser(userId, notification) {
        this.server.to(`user:${userId}`).emit('notification', notification);
        this.logger.log(`Sent notification to user ${userId}:`, notification.type);
    }
    sendNotificationToRole(role, notification) {
        this.server.to(`role:${role}`).emit('notification', notification);
        this.logger.log(`Sent notification to role ${role}:`, notification.type);
    }
    sendBroadcastNotification(notification) {
        this.server.emit('notification', notification);
        this.logger.log(`Broadcast notification:`, notification.type);
    }
    sendShiftUpdate(shiftId, update) {
        this.server.to(`shift:${shiftId}`).emit('shift-update', update);
        this.logger.log(`Sent shift update for shift ${shiftId}:`, update.type);
    }
    sendBookingUpdate(bookingId, update) {
        this.server.to(`booking:${bookingId}`).emit('booking-update', update);
        this.logger.log(`Sent booking update for booking ${bookingId}:`, update.type);
    }
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }
    isUserOnline(userId) {
        return this.connectedUsers.has(userId);
    }
};
exports.NotificationsGateway = NotificationsGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], NotificationsGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('join-room'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], NotificationsGateway.prototype, "handleJoinRoom", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave-room'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], NotificationsGateway.prototype, "handleLeaveRoom", null);
exports.NotificationsGateway = NotificationsGateway = NotificationsGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: process.env.FRONTEND_URL || 'http://localhost:5173',
            credentials: true,
        },
        namespace: '/notifications',
    }),
    __metadata("design:paramtypes", [jwt_1.JwtService])
], NotificationsGateway);
//# sourceMappingURL=notifications.gateway.js.map