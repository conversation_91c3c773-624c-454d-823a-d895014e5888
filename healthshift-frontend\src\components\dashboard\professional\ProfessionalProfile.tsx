import React from 'react';
import { User, Star, Award, Clock } from 'lucide-react';

export function ProfessionalProfile() {
  return (
    <div className="space-y-6">
      <div className="card">
        <div className="flex items-center space-x-4 mb-6">
          <div className="h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center">
            <User className="h-10 w-10 text-primary-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Professional Profile</h1>
            <p className="text-gray-600">Manage your professional information and credentials</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Star className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">4.8</div>
            <div className="text-sm text-gray-600">Average Rating</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Award className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">127</div>
            <div className="text-sm text-gray-600">Completed Shifts</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Clock className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">1,016</div>
            <div className="text-sm text-gray-600">Hours Worked</div>
          </div>
        </div>
      </div>
      
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Profile Information</h2>
        <p className="text-gray-600">
          This page would contain forms to edit professional information, certifications, 
          experience, and other profile details. In a complete implementation, this would 
          include file uploads for credentials, availability settings, and more.
        </p>
      </div>
    </div>
  );
}
