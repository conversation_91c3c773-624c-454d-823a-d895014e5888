import React, { useState } from 'react';
import { 
  Search, 
  Filter, 
  MapPin, 
  Clock, 
  DollarSign, 
  Calendar,
  Star,
  Building,
  ChevronDown
} from 'lucide-react';
import { formatCurrency, formatDateTime, getStatusColor } from '../../../lib/utils';

// Mock data - in a real app, this would come from API
const mockShifts = [
  {
    id: '1',
    title: 'ICU Night Shift',
    facility: {
      name: 'City General Hospital',
      type: 'hospital',
      rating: 4.5,
      location: 'Springfield, IL',
    },
    description: 'Critical care nursing position for night shift in our 24-bed ICU. Experience with ventilators and cardiac monitoring required.',
    specialization: 'registered_nurse',
    startDateTime: '2024-01-15T19:00:00Z',
    endDateTime: '2024-01-16T07:00:00Z',
    hourlyRate: 48.00,
    status: 'open',
    requirements: ['ICU Experience', 'ACLS Certification', 'Ventilator Training'],
  },
  {
    id: '2',
    title: 'Weekend Clinic Coverage',
    facility: {
      name: 'Sunnydale Family Clinic',
      type: 'clinic',
      rating: 4.3,
      location: 'Springfield, IL',
    },
    description: 'General nursing duties for weekend clinic coverage. Assist with patient care and medication administration.',
    specialization: 'licensed_practical_nurse',
    startDateTime: '2024-01-13T08:00:00Z',
    endDateTime: '2024-01-13T16:00:00Z',
    hourlyRate: 35.00,
    status: 'open',
    requirements: ['Medication Administration', 'Patient Assessment'],
  },
  {
    id: '3',
    title: 'Day Shift CNA',
    facility: {
      name: 'Greenwood Nursing Home',
      type: 'nursing_home',
      rating: 4.2,
      location: 'Springfield, IL',
    },
    description: 'Provide direct patient care including ADLs, vital signs, and mobility assistance for elderly residents.',
    specialization: 'certified_nursing_assistant',
    startDateTime: '2024-01-14T07:00:00Z',
    endDateTime: '2024-01-14T15:00:00Z',
    hourlyRate: 24.00,
    status: 'open',
    requirements: ['Geriatric Experience', 'ADL Assistance', 'Mobility Training'],
  },
];

const specializations = [
  'All Specializations',
  'Registered Nurse',
  'Licensed Practical Nurse',
  'Certified Nursing Assistant',
  'Nurse Practitioner',
  'Physician Assistant',
];

const facilityTypes = [
  'All Types',
  'Hospital',
  'Clinic',
  'Nursing Home',
  'Dental Office',
  'School',
];

export function ShiftBrowser() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState('All Specializations');
  const [selectedFacilityType, setSelectedFacilityType] = useState('All Types');
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState('date');

  const calculateDuration = (start: string, end: string) => {
    const startTime = new Date(start);
    const endTime = new Date(end);
    const hours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    return hours;
  };

  const calculateEarnings = (rate: number, duration: number) => {
    return rate * duration;
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search shifts by title, facility, or location..."
                className="input pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-outline flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              <ChevronDown className={`h-4 w-4 ml-2 transform transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input"
            >
              <option value="date">Sort by Date</option>
              <option value="rate">Sort by Rate</option>
              <option value="facility">Sort by Facility</option>
            </select>
          </div>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Specialization
                </label>
                <select
                  value={selectedSpecialization}
                  onChange={(e) => setSelectedSpecialization(e.target.value)}
                  className="input"
                >
                  {specializations.map((spec) => (
                    <option key={spec} value={spec}>{spec}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Facility Type
                </label>
                <select
                  value={selectedFacilityType}
                  onChange={(e) => setSelectedFacilityType(e.target.value)}
                  className="input"
                >
                  {facilityTypes.map((type) => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date Range
                </label>
                <input
                  type="date"
                  className="input"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {mockShifts.length} available shifts
        </p>
        <div className="text-sm text-gray-500">
          Updated 5 minutes ago
        </div>
      </div>

      {/* Shift Cards */}
      <div className="space-y-4">
        {mockShifts.map((shift) => {
          const duration = calculateDuration(shift.startDateTime, shift.endDateTime);
          const totalEarnings = calculateEarnings(shift.hourlyRate, duration);
          
          return (
            <div key={shift.id} className="card hover:shadow-md transition-shadow">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {shift.title}
                      </h3>
                      <div className="flex items-center text-gray-600 mb-2">
                        <Building className="h-4 w-4 mr-1" />
                        <span className="font-medium">{shift.facility.name}</span>
                        <span className="mx-2">•</span>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 mr-1" />
                          <span>{shift.facility.rating}</span>
                        </div>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(shift.status)}`}>
                      {shift.status.charAt(0).toUpperCase() + shift.status.slice(1)}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-3 line-clamp-2">
                    {shift.description}
                  </p>
                  
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDateTime(shift.startDateTime)}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {duration}h
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {shift.facility.location}
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2 mb-3">
                    {shift.requirements.map((req, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md"
                      >
                        {req}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="lg:ml-6 lg:text-right">
                  <div className="flex lg:flex-col items-center lg:items-end justify-between lg:justify-start space-x-4 lg:space-x-0 lg:space-y-2">
                    <div>
                      <div className="flex items-center text-2xl font-bold text-green-600">
                        <DollarSign className="h-5 w-5" />
                        {shift.hourlyRate}/hr
                      </div>
                      <div className="text-sm text-gray-500">
                        Total: {formatCurrency(totalEarnings)}
                      </div>
                    </div>
                    
                    <button className="btn-primary">
                      Apply Now
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Load More */}
      <div className="text-center">
        <button className="btn-outline">
          Load More Shifts
        </button>
      </div>
    </div>
  );
}
