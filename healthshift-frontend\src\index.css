@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

* { margin: 0; padding: 0; box-sizing: border-box; }

body {
  font-family: 'Inter', sans-serif;
  background: #0f0f0f;
  color: #ffffff;
  line-height: 1.5;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* CRED-style color palette */
:root {
  --cred-black: #0f0f0f;
  --cred-dark: #1a1a1a;
  --cred-card: #1e1e1e;
  --cred-border: #2a2a2a;
  --cred-text: #ffffff;
  --cred-text-secondary: #a0a0a0;
  --cred-text-muted: #666666;
  --cred-accent: #00d4aa;
  --cred-accent-hover: #00b894;
  --cred-warning: #ff6b6b;
  --cred-success: #51cf66;
  --cred-purple: #845ef7;
  --cred-blue: #339af0;
}

/* Layout */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.min-h-screen { min-height: 100vh; }

/* Flexbox */
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Spacing */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.m-2 { margin: 0.5rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.ml-2 { margin-left: 0.5rem; }

/* Typography */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }

/* CRED-style Colors */
.text-white { color: var(--cred-text); }
.text-secondary { color: var(--cred-text-secondary); }
.text-muted { color: var(--cred-text-muted); }
.text-accent { color: var(--cred-accent); }
.text-warning { color: var(--cred-warning); }
.text-success { color: var(--cred-success); }
.text-purple { color: var(--cred-purple); }
.text-blue { color: var(--cred-blue); }

.bg-black { background-color: var(--cred-black); }
.bg-dark { background-color: var(--cred-dark); }
.bg-card { background-color: var(--cred-card); }
.bg-accent { background-color: var(--cred-accent); }
.bg-accent-hover { background-color: var(--cred-accent-hover); }
.bg-warning { background-color: var(--cred-warning); }
.bg-success { background-color: var(--cred-success); }
.bg-purple { background-color: var(--cred-purple); }
.bg-blue { background-color: var(--cred-blue); }

/* CRED-style subtle effects */
.cred-card {
  background: var(--cred-card);
  border: 1px solid var(--cred-border);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cred-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

.cred-glass {
  background: rgba(30, 30, 30, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Borders */
.border { border-width: 1px; }
.border-t { border-top-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-800 { border-color: #1f2937; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Sizing */
.w-full { width: 100%; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-12 { width: 3rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-12 { height: 3rem; }
.h-32 { height: 8rem; }

/* CRED-style Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 14px 24px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  text-align: center;
  position: relative;
  letter-spacing: -0.01em;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-primary {
  background: var(--cred-accent);
  color: var(--cred-black);
  font-weight: 700;
}

.btn-primary:hover {
  background: var(--cred-accent-hover);
  box-shadow: 0 8px 24px rgba(0, 212, 170, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--cred-text);
  border: 1px solid var(--cred-border);
}

.btn-secondary:hover {
  background: var(--cred-card);
  border-color: rgba(255, 255, 255, 0.2);
}

.btn-ghost {
  background: transparent;
  color: var(--cred-text-secondary);
  border: none;
  padding: 12px 20px;
}

.btn-ghost:hover {
  color: var(--cred-text);
  background: rgba(255, 255, 255, 0.05);
}

/* CRED-style Cards */
.card {
  background: var(--cred-card);
  border: 1px solid var(--cred-border);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

.card-premium {
  background: linear-gradient(135deg, var(--cred-card) 0%, rgba(30, 30, 30, 0.8) 100%);
  border: 1px solid rgba(0, 212, 170, 0.2);
}

/* Feature cards */
.feature-card {
  background: var(--cred-card);
  border: 1px solid var(--cred-border);
  border-radius: 20px;
  padding: 32px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cred-accent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 24px 48px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Stats cards */
.stat-card {
  background: var(--cred-card);
  border: 1px solid var(--cred-border);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Icon containers */
.icon-container {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  background: var(--cred-accent);
  color: var(--cred-black);
  font-size: 24px;
}

.icon-container-secondary {
  background: var(--cred-dark);
  color: var(--cred-accent);
  border: 1px solid var(--cred-border);
}

/* CRED-style Animations */
.transition-all { transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); }
.transition-colors { transition: background-color 0.2s, color 0.2s; }
.transition-transform { transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1); }

.animate-fade-in {
  animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-up {
  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-right {
  animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Subtle hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow:hover {
  box-shadow: 0 8px 24px rgba(0, 212, 170, 0.2);
}

/* CRED-style subtle glow */
.cred-glow {
  box-shadow: 0 0 0 1px rgba(0, 212, 170, 0.1);
}

.cred-glow:hover {
  box-shadow: 0 0 0 1px rgba(0, 212, 170, 0.3), 0 8px 24px rgba(0, 212, 170, 0.1);
}

/* CRED-style Keyframes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(24px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-24px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(24px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* CRED-style Typography */
.text-gradient {
  background: linear-gradient(135deg, var(--cred-accent) 0%, var(--cred-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-hero {
  font-size: clamp(2.5rem, 8vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-display {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.text-body-large {
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--cred-text-secondary);
}

.text-body {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--cred-text-secondary);
}

.text-caption {
  font-size: 0.875rem;
  line-height: 1.4;
  color: var(--cred-text-muted);
}

/* CRED-style spacing */
.space-y-cred > * + * {
  margin-top: 1.5rem;
}

.space-y-cred-lg > * + * {
  margin-top: 2rem;
}

/* Advanced CSS animations for GSAP enhancement */
.char {
  display: inline-block;
  transform-origin: center bottom;
  text-align: inherit;
}

.word {
  display: inline-block;
  overflow: hidden;
  text-align: inherit;
}

/* Ensure animated text maintains alignment */
.hero-title .char,
.hero-subtitle .char {
  text-align: center;
}

.text-center .char {
  text-align: center;
}

/* Magnetic button effect */
.btn-primary, .btn-secondary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before, .btn-secondary::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-primary:hover::before, .btn-secondary:hover::before {
  width: 300px;
  height: 300px;
}

/* Glitch effect for text */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: var(--cred-accent);
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: var(--cred-warning);
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 2px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, -2px);
  }
}

/* Morphing shapes */
.morph-shape {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.morph-shape:hover {
  border-radius: 50% !important;
  transform: rotate(180deg) scale(1.2);
}

/* Particle trail effect */
.particle-trail {
  position: relative;
  overflow: hidden;
}

.particle-trail::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: var(--cred-accent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: particle-float 3s infinite ease-in-out;
}

@keyframes particle-float {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -200%) scale(1);
    opacity: 1;
  }
}

/* Liquid button effect */
.liquid-btn {
  position: relative;
  overflow: hidden;
}

.liquid-btn::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--cred-accent), var(--cred-blue));
  transition: top 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.liquid-btn:hover::before {
  top: 0;
}

/* Neon glow effect */
.neon-glow {
  text-shadow:
    0 0 5px var(--cred-accent),
    0 0 10px var(--cred-accent),
    0 0 15px var(--cred-accent),
    0 0 20px var(--cred-accent);
  animation: neon-flicker 2s infinite alternate;
}

@keyframes neon-flicker {
  0%, 18%, 22%, 25%, 53%, 57%, 100% {
    text-shadow:
      0 0 5px var(--cred-accent),
      0 0 10px var(--cred-accent),
      0 0 15px var(--cred-accent),
      0 0 20px var(--cred-accent);
  }
  20%, 24%, 55% {
    text-shadow: none;
  }
}

/* Responsive */
@media (min-width: 640px) {
  .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\\:text-5xl { font-size: 3rem; }
}

@media (min-width: 768px) {
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:text-4xl { font-size: 2.25rem; }
  .md\\:text-5xl { font-size: 3rem; }
  .md\\:text-6xl { font-size: 3.75rem; }
}

@media (min-width: 1024px) {
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\\:text-6xl { font-size: 3.75rem; }
}