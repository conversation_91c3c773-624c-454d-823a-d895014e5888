import React from 'react';
import { Setting<PERSON>, <PERSON>, <PERSON>, <PERSON> } from 'lucide-react';

export function ProfessionalSettings() {
  return (
    <div className="space-y-6">
      <div className="card">
        <div className="flex items-center space-x-4 mb-6">
          <Settings className="h-8 w-8 text-primary-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your account preferences and availability</p>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="flex items-center space-x-3 mb-4">
            <Bell className="h-6 w-6 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">Notifications</h2>
          </div>
          <p className="text-gray-600">
            Configure how you want to receive notifications about new shifts, 
            booking updates, and other important information.
          </p>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-3 mb-4">
            <Clock className="h-6 w-6 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Availability</h2>
          </div>
          <p className="text-gray-600">
            Set your weekly availability schedule to help facilities find you 
            for shifts that match your preferred working hours.
          </p>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="h-6 w-6 text-purple-600" />
            <h2 className="text-lg font-semibold text-gray-900">Privacy & Security</h2>
          </div>
          <p className="text-gray-600">
            Manage your privacy settings, password, and two-factor authentication 
            to keep your account secure.
          </p>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-3 mb-4">
            <Settings className="h-6 w-6 text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-900">Account Settings</h2>
          </div>
          <p className="text-gray-600">
            Update your personal information, payment preferences, and other 
            account-related settings.
          </p>
        </div>
      </div>
    </div>
  );
}
