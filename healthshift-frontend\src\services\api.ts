import { supabase } from '../lib/supabase';
import { 
  Shift, 
  Booking, 
  HealthcareProfessional, 
  Facility, 
  Notification,
  ShiftFilters,
  PaginatedResponse 
} from '../types';

// Shifts API
export const shiftsApi = {
  async getShifts(filters?: ShiftFilters, page = 1, limit = 10): Promise<PaginatedResponse<Shift>> {
    let query = supabase
      .from('shifts')
      .select(`
        *,
        facility:facilities(
          id,
          facility_name,
          facility_type,
          address,
          contact_phone,
          rating,
          user:users(first_name, last_name, email)
        ),
        professional:healthcare_professionals(
          id,
          user:users(first_name, last_name, email),
          specialization,
          rating
        )
      `)
      .order('start_date_time', { ascending: true });

    // Apply filters
    if (filters?.specialization) {
      query = query.eq('specialization', filters.specialization);
    }
    if (filters?.startDate) {
      query = query.gte('start_date_time', filters.startDate);
    }
    if (filters?.endDate) {
      query = query.lte('start_date_time', filters.endDate);
    }
    if (filters?.minRate) {
      query = query.gte('hourly_rate', filters.minRate);
    }
    if (filters?.maxRate) {
      query = query.lte('hourly_rate', filters.maxRate);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      data: data?.map(transformShiftData) || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    };
  },

  async getShiftById(id: string): Promise<Shift> {
    const { data, error } = await supabase
      .from('shifts')
      .select(`
        *,
        facility:facilities(
          id,
          facility_name,
          facility_type,
          address,
          contact_phone,
          rating,
          user:users(first_name, last_name, email)
        ),
        professional:healthcare_professionals(
          id,
          user:users(first_name, last_name, email),
          specialization,
          rating
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return transformShiftData(data);
  },

  async createShift(shiftData: Omit<Shift, 'id' | 'createdAt' | 'updatedAt' | 'facility' | 'professional'>): Promise<Shift> {
    const { data, error } = await supabase
      .from('shifts')
      .insert({
        facility_id: shiftData.facilityId,
        title: shiftData.title,
        description: shiftData.description,
        specialization: shiftData.specialization,
        start_date_time: shiftData.startDateTime,
        end_date_time: shiftData.endDateTime,
        hourly_rate: shiftData.hourlyRate,
        requirements: shiftData.requirements,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return transformShiftData(data);
  },

  async updateShift(id: string, updates: Partial<Shift>): Promise<Shift> {
    const { data, error } = await supabase
      .from('shifts')
      .update({
        title: updates.title,
        description: updates.description,
        specialization: updates.specialization,
        start_date_time: updates.startDateTime,
        end_date_time: updates.endDateTime,
        hourly_rate: updates.hourlyRate,
        status: updates.status,
        requirements: updates.requirements,
        booked_by: updates.bookedBy,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return transformShiftData(data);
  },

  async deleteShift(id: string): Promise<void> {
    const { error } = await supabase
      .from('shifts')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }
  },
};

// Bookings API
export const bookingsApi = {
  async getBookings(page = 1, limit = 10): Promise<PaginatedResponse<Booking>> {
    const { data, error, count } = await supabase
      .from('bookings')
      .select(`
        *,
        shift:shifts(
          *,
          facility:facilities(
            id,
            facility_name,
            facility_type,
            address,
            user:users(first_name, last_name, email)
          )
        ),
        professional:healthcare_professionals(
          id,
          user:users(first_name, last_name, email),
          specialization,
          rating
        )
      `)
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      throw new Error(error.message);
    }

    return {
      data: data?.map(transformBookingData) || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    };
  },

  async createBooking(shiftId: string, professionalId: string, notes?: string): Promise<Booking> {
    const { data, error } = await supabase
      .from('bookings')
      .insert({
        shift_id: shiftId,
        professional_id: professionalId,
        notes,
      })
      .select(`
        *,
        shift:shifts(
          *,
          facility:facilities(
            id,
            facility_name,
            facility_type,
            address,
            user:users(first_name, last_name, email)
          )
        ),
        professional:healthcare_professionals(
          id,
          user:users(first_name, last_name, email),
          specialization,
          rating
        )
      `)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return transformBookingData(data);
  },

  async updateBookingStatus(id: string, status: Booking['status']): Promise<Booking> {
    const { data, error } = await supabase
      .from('bookings')
      .update({ status })
      .eq('id', id)
      .select(`
        *,
        shift:shifts(
          *,
          facility:facilities(
            id,
            facility_name,
            facility_type,
            address,
            user:users(first_name, last_name, email)
          )
        ),
        professional:healthcare_professionals(
          id,
          user:users(first_name, last_name, email),
          specialization,
          rating
        )
      `)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return transformBookingData(data);
  },
};

// Professionals API
export const professionalsApi = {
  async getProfessionals(page = 1, limit = 10): Promise<PaginatedResponse<HealthcareProfessional>> {
    const { data, error, count } = await supabase
      .from('healthcare_professionals')
      .select(`
        *,
        user:users(*)
      `)
      .order('rating', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      throw new Error(error.message);
    }

    return {
      data: data?.map(transformProfessionalData) || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    };
  },

  async getProfessionalById(id: string): Promise<HealthcareProfessional> {
    const { data, error } = await supabase
      .from('healthcare_professionals')
      .select(`
        *,
        user:users(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return transformProfessionalData(data);
  },
};

// Notifications API
export const notificationsApi = {
  async getNotifications(page = 1, limit = 10): Promise<PaginatedResponse<Notification>> {
    const { data, error, count } = await supabase
      .from('notifications')
      .select('*')
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      throw new Error(error.message);
    }

    return {
      data: data?.map(transformNotificationData) || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    };
  },

  async markAsRead(id: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }
  },

  async markAllAsRead(): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('read', false);

    if (error) {
      throw new Error(error.message);
    }
  },
};

// Transform functions to convert database format to frontend types
function transformShiftData(data: any): Shift {
  return {
    id: data.id,
    facilityId: data.facility_id,
    facility: data.facility ? {
      id: data.facility.id,
      email: data.facility.user.email,
      firstName: data.facility.user.first_name,
      lastName: data.facility.user.last_name,
      role: 'facility',
      facilityName: data.facility.facility_name,
      facilityType: data.facility.facility_type,
      address: data.facility.address,
      contactPhone: data.facility.contact_phone,
      licenseNumber: data.facility.license_number || '',
      rating: data.facility.rating,
      totalShiftsPosted: data.facility.total_shifts_posted || 0,
      createdAt: data.facility.created_at || '',
      updatedAt: data.facility.updated_at || '',
    } : undefined,
    title: data.title,
    description: data.description,
    specialization: data.specialization,
    startDateTime: data.start_date_time,
    endDateTime: data.end_date_time,
    hourlyRate: data.hourly_rate,
    status: data.status,
    requirements: data.requirements || [],
    bookedBy: data.booked_by,
    professional: data.professional ? transformProfessionalData(data.professional) : undefined,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

function transformBookingData(data: any): Booking {
  return {
    id: data.id,
    shiftId: data.shift_id,
    shift: transformShiftData(data.shift),
    professionalId: data.professional_id,
    professional: transformProfessionalData(data.professional),
    status: data.status,
    notes: data.notes,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

function transformProfessionalData(data: any): HealthcareProfessional {
  return {
    id: data.user?.id || data.id,
    email: data.user?.email || '',
    firstName: data.user?.first_name || '',
    lastName: data.user?.last_name || '',
    role: 'professional',
    licenseNumber: data.license_number,
    specialization: data.specialization,
    experience: data.experience,
    hourlyRate: data.hourly_rate,
    availability: [], // Would need separate query for availability
    certifications: data.certifications || [],
    profileImage: data.profile_image,
    bio: data.bio,
    rating: data.rating,
    completedShifts: data.completed_shifts,
    createdAt: data.created_at || data.user?.created_at || '',
    updatedAt: data.updated_at || data.user?.updated_at || '',
  };
}

function transformNotificationData(data: any): Notification {
  return {
    id: data.id,
    userId: data.user_id,
    type: data.type,
    title: data.title,
    message: data.message,
    read: data.read,
    relatedId: data.related_id,
    createdAt: data.created_at,
  };
}
