{"hash": "912a9d43", "configHash": "38d7c050", "lockfileHash": "cbd7a324", "browserHash": "55087b7f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "9eee028a", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c80b4d6a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d1e8e289", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "fe1a8228", "needsInterop": true}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "16d857d0", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "5b518e66", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "79bf1e2c", "needsInterop": false}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "984f1d1e", "needsInterop": false}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "fe836a49", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "50d807e3", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "07aee476", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "543159b0", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "b5970052", "needsInterop": false}}, "chunks": {"browser-EMB7CRMN": {"file": "browser-EMB7CRMN.js"}, "chunk-Y5BGZF4O": {"file": "chunk-Y5BGZF4O.js"}, "chunk-5H4R2CZR": {"file": "chunk-5H4R2CZR.js"}, "chunk-VTIQK5XW": {"file": "chunk-VTIQK5XW.js"}, "chunk-H5FQS3OF": {"file": "chunk-H5FQS3OF.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}