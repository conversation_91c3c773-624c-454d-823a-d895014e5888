{"name": "symbol-observable", "version": "4.0.0", "description": "Symbol.observable ponyfill", "license": "MIT", "repository": "blesh/symbol-observable", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"test": "npm run build && mocha && tsc && node ./ts-test/test.js && check-es3-syntax -p lib/ --kill", "build": "babel es --out-dir lib", "prepublish": "npm test"}, "files": ["index.js", "ponyfill.js", "index.d.ts", "ponyfill.d.ts", "es/index.js", "es/ponyfill.js", "lib/index.js", "lib/ponyfill.js"], "main": "lib/index.js", "module": "es/index.js", "jsnext:main": "es/index.js", "typings": "index.d.ts", "keywords": ["symbol", "observable", "observables", "ponyfill", "polyfill", "shim"], "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-es2015": "^6.9.0", "babel-preset-es3": "^1.0.0", "chai": "^3.5.0", "check-es3-syntax-cli": "^0.1.0", "mocha": "^2.4.5", "typescript": "^4.0.5"}, "engines": {"node": ">=0.10"}}