import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { supabaseService } from '../services/supabase';
import { useAuth } from './AuthContext';

// Local interface to avoid import issues
interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  related_id?: string;
  created_at: string;
}

interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  addNotification: (notification: Notification) => void;
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

export function useNotifications() {
  const context = useContext(NotificationsContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
}

interface NotificationsProviderProps {
  children: React.ReactNode;
}

export function NotificationsProvider({ children }: NotificationsProviderProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const { isAuthenticated, user } = useAuth();

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  // Load notifications when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadNotifications();
      setIsConnected(true);
    } else {
      setNotifications([]);
      setIsConnected(false);
    }
  }, [isAuthenticated, user]);

  // Set up Supabase real-time subscription for notifications
  useEffect(() => {
    if (isAuthenticated && user) {
      const subscription = supabaseService.subscribeToNotifications(user.id, (payload) => {
        if (payload.eventType === 'INSERT') {
          const newNotification = payload.new as Notification;
          addNotification(newNotification);

          // Show browser notification if permission is granted
          if (window.Notification && window.Notification.permission === 'granted') {
            new window.Notification(newNotification.title, {
              body: newNotification.message,
              icon: '/favicon.ico',
              tag: newNotification.id,
            });
          }
        }
      });

      return () => {
        subscription.unsubscribe();
      };
    }
  }, [isAuthenticated, user]);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && window.Notification.permission === 'default') {
      window.Notification.requestPermission();
    }
  }, []);

  const loadNotifications = useCallback(async () => {
    try {
      const data = await supabaseService.getNotifications();
      setNotifications(data || []);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    }
  }, []);

  const addNotification = useCallback((notification: Notification) => {
    setNotifications(prev => {
      // Check if notification already exists
      const exists = prev.some(n => n.id === notification.id);
      if (exists) return prev;

      // Add new notification at the beginning
      const newNotifications = [{ ...notification, read: false }, ...prev];
      
      // Keep only the last 100 notifications
      return newNotifications.slice(0, 100);
    });
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await supabaseService.markNotificationAsRead(notificationId);
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification
        )
      );
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      await supabaseService.markAllNotificationsAsRead();
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Load initial notifications when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      // In a real app, you'd fetch notifications from the API
      // For now, we'll add some mock notifications
      const mockNotifications: NotificationData[] = [
        {
          id: 'notif-1',
          type: 'shift_confirmed',
          title: 'Shift Confirmed',
          message: 'Your booking for Emergency Department RN shift has been confirmed.',
          timestamp: new Date().toISOString(),
          read: false,
        },
        {
          id: 'notif-2',
          type: 'new_shift_available',
          title: 'New Shift Available',
          message: 'A new ICU shift matching your specialization is available.',
          timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          read: false,
        },
        {
          id: 'notif-3',
          type: 'reminder',
          title: 'Shift Reminder',
          message: 'Your shift starts in 2 hours.',
          timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
          read: true,
        },
      ];

      setNotifications(mockNotifications);
    } else {
      setNotifications([]);
    }
  }, [isAuthenticated, user]);

  const value: NotificationsContextType = {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    addNotification,
  };

  return (
    <NotificationsContext.Provider value={value}>
      {children}
    </NotificationsContext.Provider>
  );
}
