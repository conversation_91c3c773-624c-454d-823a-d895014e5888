import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ProfessionalLayout } from './ProfessionalLayout';
import { ProfessionalOverview } from './professional/ProfessionalOverview';
import { ShiftBrowser } from './professional/ShiftBrowser';
import { BookingHistory } from './professional/BookingHistory';
import { ProfessionalProfile } from './professional/ProfessionalProfile';
import { ProfessionalSettings } from './professional/ProfessionalSettings';

export function ProfessionalDashboard() {
  return (
    <ProfessionalLayout>
      <Routes>
        <Route index element={<ProfessionalOverview />} />
        <Route path="shifts" element={<ShiftBrowser />} />
        <Route path="bookings" element={<BookingHistory />} />
        <Route path="profile" element={<ProfessionalProfile />} />
        <Route path="settings" element={<ProfessionalSettings />} />
        <Route path="*" element={<Navigate to="/professional" replace />} />
      </Routes>
    </ProfessionalLayout>
  );
}
