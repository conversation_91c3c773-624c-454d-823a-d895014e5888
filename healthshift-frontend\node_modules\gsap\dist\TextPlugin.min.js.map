{"version": 3, "file": "TextPlugin.min.js", "sources": ["../src/utils/strings.js", "../src/TextPlugin.js"], "sourcesContent": ["/*!\n * strings: 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _trimExp = /(?:^\\s+|\\s+$)/g;\n\nexport const emojiExp = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF](?:[\\u200D\\uFE0F][\\uD800-\\uDBFF][\\uDC00-\\uDFFF]){2,}|\\uD83D\\uDC69(?:\\u200D(?:(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC67|(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC66)|\\uD83C[\\uDFFB-\\uDFFF])|\\uD83D\\uDC69\\u200D(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC66\\u200D\\uD83D\\uDC66|\\uD83D\\uDC69\\u200D(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC67\\u200D(?:\\uD83D[\\uDC66\\uDC67])|\\uD83C\\uDFF3\\uFE0F\\u200D\\uD83C\\uDF08|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3D\\uDD3E\\uDDD6-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2642\\u2640]\\uFE0F|\\uD83D\\uDC69(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDD27\\uDCBC\\uDD2C\\uDE80\\uDE92])|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC6F\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3C-\\uDD3E\\uDDD6-\\uDDDF])\\u200D[\\u2640\\u2642]\\uFE0F|\\uD83C\\uDDFD\\uD83C\\uDDF0|\\uD83C\\uDDF6\\uD83C\\uDDE6|\\uD83C\\uDDF4\\uD83C\\uDDF2|\\uD83C\\uDDE9(?:\\uD83C[\\uDDEA\\uDDEC\\uDDEF\\uDDF0\\uDDF2\\uDDF4\\uDDFF])|\\uD83C\\uDDF7(?:\\uD83C[\\uDDEA\\uDDF4\\uDDF8\\uDDFA\\uDDFC])|\\uD83C\\uDDE8(?:\\uD83C[\\uDDE6\\uDDE8\\uDDE9\\uDDEB-\\uDDEE\\uDDF0-\\uDDF5\\uDDF7\\uDDFA-\\uDDFF])|(?:\\u26F9|\\uD83C[\\uDFCC\\uDFCB]|\\uD83D\\uDD75)(?:\\uFE0F\\u200D[\\u2640\\u2642]|(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2640\\u2642])\\uFE0F|(?:\\uD83D\\uDC41\\uFE0F\\u200D\\uD83D\\uDDE8|\\uD83D\\uDC69(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2695\\u2696\\u2708]|\\uD83D\\uDC69\\u200D[\\u2695\\u2696\\u2708]|\\uD83D\\uDC68(?:(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2695\\u2696\\u2708]|\\u200D[\\u2695\\u2696\\u2708]))\\uFE0F|\\uD83C\\uDDF2(?:\\uD83C[\\uDDE6\\uDDE8-\\uDDED\\uDDF0-\\uDDFF])|\\uD83D\\uDC69\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92]|\\u2764\\uFE0F\\u200D(?:\\uD83D\\uDC8B\\u200D(?:\\uD83D[\\uDC68\\uDC69])|\\uD83D[\\uDC68\\uDC69]))|\\uD83C\\uDDF1(?:\\uD83C[\\uDDE6-\\uDDE8\\uDDEE\\uDDF0\\uDDF7-\\uDDFB\\uDDFE])|\\uD83C\\uDDEF(?:\\uD83C[\\uDDEA\\uDDF2\\uDDF4\\uDDF5])|\\uD83C\\uDDED(?:\\uD83C[\\uDDF0\\uDDF2\\uDDF3\\uDDF7\\uDDF9\\uDDFA])|\\uD83C\\uDDEB(?:\\uD83C[\\uDDEE-\\uDDF0\\uDDF2\\uDDF4\\uDDF7])|[#\\*0-9]\\uFE0F\\u20E3|\\uD83C\\uDDE7(?:\\uD83C[\\uDDE6\\uDDE7\\uDDE9-\\uDDEF\\uDDF1-\\uDDF4\\uDDF6-\\uDDF9\\uDDFB\\uDDFC\\uDDFE\\uDDFF])|\\uD83C\\uDDE6(?:\\uD83C[\\uDDE8-\\uDDEC\\uDDEE\\uDDF1\\uDDF2\\uDDF4\\uDDF6-\\uDDFA\\uDDFC\\uDDFD\\uDDFF])|\\uD83C\\uDDFF(?:\\uD83C[\\uDDE6\\uDDF2\\uDDFC])|\\uD83C\\uDDF5(?:\\uD83C[\\uDDE6\\uDDEA-\\uDDED\\uDDF0-\\uDDF3\\uDDF7-\\uDDF9\\uDDFC\\uDDFE])|\\uD83C\\uDDFB(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA\\uDDEC\\uDDEE\\uDDF3\\uDDFA])|\\uD83C\\uDDF3(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA-\\uDDEC\\uDDEE\\uDDF1\\uDDF4\\uDDF5\\uDDF7\\uDDFA\\uDDFF])|\\uD83C\\uDFF4\\uDB40\\uDC67\\uDB40\\uDC62(?:\\uDB40\\uDC77\\uDB40\\uDC6C\\uDB40\\uDC73|\\uDB40\\uDC73\\uDB40\\uDC63\\uDB40\\uDC74|\\uDB40\\uDC65\\uDB40\\uDC6E\\uDB40\\uDC67)\\uDB40\\uDC7F|\\uD83D\\uDC68(?:\\u200D(?:\\u2764\\uFE0F\\u200D(?:\\uD83D\\uDC8B\\u200D)?\\uD83D\\uDC68|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC66\\u200D\\uD83D\\uDC66|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC67\\u200D(?:\\uD83D[\\uDC66\\uDC67])|\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92])|(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92]))|\\uD83C\\uDDF8(?:\\uD83C[\\uDDE6-\\uDDEA\\uDDEC-\\uDDF4\\uDDF7-\\uDDF9\\uDDFB\\uDDFD-\\uDDFF])|\\uD83C\\uDDF0(?:\\uD83C[\\uDDEA\\uDDEC-\\uDDEE\\uDDF2\\uDDF3\\uDDF5\\uDDF7\\uDDFC\\uDDFE\\uDDFF])|\\uD83C\\uDDFE(?:\\uD83C[\\uDDEA\\uDDF9])|\\uD83C\\uDDEE(?:\\uD83C[\\uDDE8-\\uDDEA\\uDDF1-\\uDDF4\\uDDF6-\\uDDF9])|\\uD83C\\uDDF9(?:\\uD83C[\\uDDE6\\uDDE8\\uDDE9\\uDDEB-\\uDDED\\uDDEF-\\uDDF4\\uDDF7\\uDDF9\\uDDFB\\uDDFC\\uDDFF])|\\uD83C\\uDDEC(?:\\uD83C[\\uDDE6\\uDDE7\\uDDE9-\\uDDEE\\uDDF1-\\uDDF3\\uDDF5-\\uDDFA\\uDDFC\\uDDFE])|\\uD83C\\uDDFA(?:\\uD83C[\\uDDE6\\uDDEC\\uDDF2\\uDDF3\\uDDF8\\uDDFE\\uDDFF])|\\uD83C\\uDDEA(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA\\uDDEC\\uDDED\\uDDF7-\\uDDFA])|\\uD83C\\uDDFC(?:\\uD83C[\\uDDEB\\uDDF8])|(?:\\u26F9|\\uD83C[\\uDFCB\\uDFCC]|\\uD83D\\uDD75)(?:\\uD83C[\\uDFFB-\\uDFFF])|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3D\\uDD3E\\uDDD6-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])|(?:[\\u261D\\u270A-\\u270D]|\\uD83C[\\uDF85\\uDFC2\\uDFC7]|\\uD83D[\\uDC42\\uDC43\\uDC46-\\uDC50\\uDC66\\uDC67\\uDC70\\uDC72\\uDC74-\\uDC76\\uDC78\\uDC7C\\uDC83\\uDC85\\uDCAA\\uDD74\\uDD7A\\uDD90\\uDD95\\uDD96\\uDE4C\\uDE4F\\uDEC0\\uDECC]|\\uD83E[\\uDD18-\\uDD1C\\uDD1E\\uDD1F\\uDD30-\\uDD36\\uDDD1-\\uDDD5])(?:\\uD83C[\\uDFFB-\\uDFFF])|\\uD83D\\uDC68(?:\\u200D(?:(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC67|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC66)|\\uD83C[\\uDFFB-\\uDFFF])|(?:[\\u261D\\u26F9\\u270A-\\u270D]|\\uD83C[\\uDF85\\uDFC2-\\uDFC4\\uDFC7\\uDFCA-\\uDFCC]|\\uD83D[\\uDC42\\uDC43\\uDC46-\\uDC50\\uDC66-\\uDC69\\uDC6E\\uDC70-\\uDC78\\uDC7C\\uDC81-\\uDC83\\uDC85-\\uDC87\\uDCAA\\uDD74\\uDD75\\uDD7A\\uDD90\\uDD95\\uDD96\\uDE45-\\uDE47\\uDE4B-\\uDE4F\\uDEA3\\uDEB4-\\uDEB6\\uDEC0\\uDECC]|\\uD83E[\\uDD18-\\uDD1C\\uDD1E\\uDD1F\\uDD26\\uDD30-\\uDD39\\uDD3D\\uDD3E\\uDDD1-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])?|(?:[\\u231A\\u231B\\u23E9-\\u23EC\\u23F0\\u23F3\\u25FD\\u25FE\\u2614\\u2615\\u2648-\\u2653\\u267F\\u2693\\u26A1\\u26AA\\u26AB\\u26BD\\u26BE\\u26C4\\u26C5\\u26CE\\u26D4\\u26EA\\u26F2\\u26F3\\u26F5\\u26FA\\u26FD\\u2705\\u270A\\u270B\\u2728\\u274C\\u274E\\u2753-\\u2755\\u2757\\u2795-\\u2797\\u27B0\\u27BF\\u2B1B\\u2B1C\\u2B50\\u2B55]|\\uD83C[\\uDC04\\uDCCF\\uDD8E\\uDD91-\\uDD9A\\uDDE6-\\uDDFF\\uDE01\\uDE1A\\uDE2F\\uDE32-\\uDE36\\uDE38-\\uDE3A\\uDE50\\uDE51\\uDF00-\\uDF20\\uDF2D-\\uDF35\\uDF37-\\uDF7C\\uDF7E-\\uDF93\\uDFA0-\\uDFCA\\uDFCF-\\uDFD3\\uDFE0-\\uDFF0\\uDFF4\\uDFF8-\\uDFFF]|\\uD83D[\\uDC00-\\uDC3E\\uDC40\\uDC42-\\uDCFC\\uDCFF-\\uDD3D\\uDD4B-\\uDD4E\\uDD50-\\uDD67\\uDD7A\\uDD95\\uDD96\\uDDA4\\uDDFB-\\uDE4F\\uDE80-\\uDEC5\\uDECC\\uDED0-\\uDED2\\uDEEB\\uDEEC\\uDEF4-\\uDEF8]|\\uD83E[\\uDD10-\\uDD3A\\uDD3C-\\uDD3E\\uDD40-\\uDD45\\uDD47-\\uDD4C\\uDD50-\\uDD6B\\uDD80-\\uDD97\\uDDC0\\uDDD0-\\uDDE6])|(?:[#\\*0-9\\xA9\\xAE\\u203C\\u2049\\u2122\\u2139\\u2194-\\u2199\\u21A9\\u21AA\\u231A\\u231B\\u2328\\u23CF\\u23E9-\\u23F3\\u23F8-\\u23FA\\u24C2\\u25AA\\u25AB\\u25B6\\u25C0\\u25FB-\\u25FE\\u2600-\\u2604\\u260E\\u2611\\u2614\\u2615\\u2618\\u261D\\u2620\\u2622\\u2623\\u2626\\u262A\\u262E\\u262F\\u2638-\\u263A\\u2640\\u2642\\u2648-\\u2653\\u2660\\u2663\\u2665\\u2666\\u2668\\u267B\\u267F\\u2692-\\u2697\\u2699\\u269B\\u269C\\u26A0\\u26A1\\u26AA\\u26AB\\u26B0\\u26B1\\u26BD\\u26BE\\u26C4\\u26C5\\u26C8\\u26CE\\u26CF\\u26D1\\u26D3\\u26D4\\u26E9\\u26EA\\u26F0-\\u26F5\\u26F7-\\u26FA\\u26FD\\u2702\\u2705\\u2708-\\u270D\\u270F\\u2712\\u2714\\u2716\\u271D\\u2721\\u2728\\u2733\\u2734\\u2744\\u2747\\u274C\\u274E\\u2753-\\u2755\\u2757\\u2763\\u2764\\u2795-\\u2797\\u27A1\\u27B0\\u27BF\\u2934\\u2935\\u2B05-\\u2B07\\u2B1B\\u2B1C\\u2B50\\u2B55\\u3030\\u303D\\u3297\\u3299]|\\uD83C[\\uDC04\\uDCCF\\uDD70\\uDD71\\uDD7E\\uDD7F\\uDD8E\\uDD91-\\uDD9A\\uDDE6-\\uDDFF\\uDE01\\uDE02\\uDE1A\\uDE2F\\uDE32-\\uDE3A\\uDE50\\uDE51\\uDF00-\\uDF21\\uDF24-\\uDF93\\uDF96\\uDF97\\uDF99-\\uDF9B\\uDF9E-\\uDFF0\\uDFF3-\\uDFF5\\uDFF7-\\uDFFF]|\\uD83D[\\uDC00-\\uDCFD\\uDCFF-\\uDD3D\\uDD49-\\uDD4E\\uDD50-\\uDD67\\uDD6F\\uDD70\\uDD73-\\uDD7A\\uDD87\\uDD8A-\\uDD8D\\uDD90\\uDD95\\uDD96\\uDDA4\\uDDA5\\uDDA8\\uDDB1\\uDDB2\\uDDBC\\uDDC2-\\uDDC4\\uDDD1-\\uDDD3\\uDDDC-\\uDDDE\\uDDE1\\uDDE3\\uDDE8\\uDDEF\\uDDF3\\uDDFA-\\uDE4F\\uDE80-\\uDEC5\\uDECB-\\uDED2\\uDEE0-\\uDEE5\\uDEE9\\uDEEB\\uDEEC\\uDEF0\\uDEF3-\\uDEF8]|\\uD83E[\\uDD10-\\uDD3A\\uDD3C-\\uDD3E\\uDD40-\\uDD45\\uDD47-\\uDD4C\\uDD50-\\uDD6B\\uDD80-\\uDD97\\uDDC0\\uDDD0-\\uDDE6])\\uFE0F)/;\n\nexport function getText(e) {\n\tlet type = e.nodeType,\n\t\tresult = \"\";\n\tif (type === 1 || type === 9 || type === 11) {\n\t\tif (typeof(e.textContent) === \"string\") {\n\t\t\treturn e.textContent;\n\t\t} else {\n\t\t\tfor (e = e.firstChild; e; e = e.nextSibling ) {\n\t\t\t\tresult += getText(e);\n\t\t\t}\n\t\t}\n\t} else if (type === 3 || type === 4) {\n\t\treturn e.nodeValue;\n\t}\n\treturn result;\n}\n\nexport function splitInnerHTML(element, delimiter, trim, preserveSpaces, unescapedCharCodes) {\n\tlet node = element.firstChild,\n\t\tresult = [], s;\n\twhile (node) {\n\t\tif (node.nodeType === 3) {\n\t\t\ts = (node.nodeValue + \"\").replace(/^\\n+/g, \"\");\n\t\t\tif (!preserveSpaces) {\n\t\t\t\ts = s.replace(/\\s+/g, \" \");\n\t\t\t}\n\t\t\tresult.push(...emojiSafeSplit(s, delimiter, trim, preserveSpaces, unescapedCharCodes));\n\t\t} else if ((node.nodeName + \"\").toLowerCase() === \"br\") {\n\t\t\tresult[result.length-1] += \"<br>\";\n\t\t} else {\n\t\t\tresult.push(node.outerHTML);\n\t\t}\n\t\tnode = node.nextSibling;\n\t}\n\tif (!unescapedCharCodes) {\n\t\ts = result.length;\n\t\twhile (s--) {\n\t\t\tresult[s] === \"&\" && result.splice(s, 1, \"&amp;\");\n\t\t}\n\t}\n\treturn result;\n}\n\n/*\n//smaller kb version that only handles the simpler emoji's, which is often perfectly adequate.\n\nlet _emoji = \"[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2694-\\u2697]|\\uD83E[\\uDD10-\\uDD5D]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]\",\n\t_emojiExp = new RegExp(_emoji),\n\t_emojiAndCharsExp = new RegExp(_emoji + \"|.\", \"g\"),\n\t_emojiSafeSplit = (text, delimiter, trim) => {\n\t\tif (trim) {\n\t\t\ttext = text.replace(_trimExp, \"\");\n\t\t}\n\t\treturn ((delimiter === \"\" || !delimiter) && _emojiExp.test(text)) ? text.match(_emojiAndCharsExp) : text.split(delimiter || \"\");\n\t};\n */\nexport function emojiSafeSplit(text, delimiter, trim, preserveSpaces, unescapedCharCodes) {\n\ttext += \"\"; // make sure it's cast as a string. Someone may pass in a number.\n\ttrim && (text = text.trim ? text.trim() : text.replace(_trimExp, \"\")); // IE9 and earlier compatibility\n\tif (delimiter && delimiter !== \"\") {\n\t\treturn text.replace(/>/g, \"&gt;\").replace(/</g, \"&lt;\").split(delimiter);\n\t}\n\tlet result = [],\n\t\tl = text.length,\n\t\ti = 0,\n\t\tj, character;\n\tfor (; i < l; i++) {\n\t\tcharacter = text.charAt(i);\n\t\tif ((character.charCodeAt(0) >= 0xD800 && character.charCodeAt(0) <= 0xDBFF) || (text.charCodeAt(i+1) >= 0xFE00 && text.charCodeAt(i+1) <= 0xFE0F)) { //special emoji characters use 2 or 4 unicode characters that we must keep together.\n\t\t\tj = ((text.substr(i, 12).split(emojiExp) || [])[1] || \"\").length || 2;\n\t\t\tcharacter = text.substr(i, j);\n\t\t\tresult.emoji = 1;\n\t\t\ti += j - 1;\n\t\t}\n\t\tresult.push(unescapedCharCodes ? character : character === \">\" ? \"&gt;\" : (character === \"<\") ? \"&lt;\" : preserveSpaces && character === \" \" && (text.charAt(i-1) === \" \" || text.charAt(i+1) === \" \") ? \"&nbsp;\" : character);\n\t}\n\treturn result;\n}", "/*!\n * TextPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { emojiSafeSplit, getText, splitInnerHTML } from \"./utils/strings.js\";\n\nlet gsap, _tempDiv,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap);\n\n\nexport const TextPlugin = {\n\tversion:\"3.13.0\",\n\tname:\"text\",\n\tinit(target, value, tween) {\n\t\ttypeof(value) !== \"object\" && (value = {value:value});\n\t\tlet i = target.nodeName.toUpperCase(),\n\t\t\tdata = this,\n\t\t\t{ newClass, oldClass, preserveSpaces, rtl } = value,\n\t\t\tdelimiter = data.delimiter = value.delimiter || \"\",\n\t\t\tfillChar = data.fillChar = value.fillChar || (value.padSpace ? \"&nbsp;\" : \"\"),\n\t\t\tshort, text, original, j, condensedText, condensedOriginal, aggregate, s;\n\t\tdata.svg = (target.getBBox && (i === \"TEXT\" || i === \"TSPAN\"));\n\t\tif (!(\"innerHTML\" in target) && !data.svg) {\n\t\t\treturn false;\n\t\t}\n\t\tdata.target = target;\n\t\tif (!(\"value\" in value)) {\n\t\t\tdata.text = data.original = [\"\"];\n\t\t\treturn;\n\t\t}\n\t\toriginal = splitInnerHTML(target, delimiter, false, preserveSpaces, data.svg);\n\t\t_tempDiv || (_tempDiv = document.createElement(\"div\"));\n\t\t_tempDiv.innerHTML = value.value;\n\t\ttext = splitInnerHTML(_tempDiv, delimiter, false, preserveSpaces, data.svg);\n\t\tdata.from = tween._from;\n\t\tif ((data.from || rtl) && !(rtl && data.from)) { // right-to-left or \"from()\" tweens should invert things (but if it's BOTH .from() and rtl, inverting twice equals not inverting at all :)\n\t\t\ti = original;\n\t\t\toriginal = text;\n\t\t\ttext = i;\n\t\t}\n\t\tdata.hasClass = !!(newClass || oldClass);\n\t\tdata.newClass = rtl ? oldClass : newClass;\n\t\tdata.oldClass = rtl ? newClass : oldClass;\n\t\ti = original.length - text.length;\n\t\tshort = i < 0 ? original : text;\n\t\tif (i < 0) {\n\t\t\ti = -i;\n\t\t}\n\t\twhile (--i > -1) {\n\t\t\tshort.push(fillChar);\n\t\t}\n\t\tif (value.type === \"diff\") {\n\t\t\tj = 0;\n\t\t\tcondensedText = [];\n\t\t\tcondensedOriginal = [];\n\t\t\taggregate = \"\";\n\t\t\tfor (i = 0; i < text.length; i++) {\n\t\t\t\ts = text[i];\n\t\t\t\tif (s === original[i]) {\n\t\t\t\t\taggregate += s;\n\t\t\t\t} else {\n\t\t\t\t\tcondensedText[j] = aggregate + s;\n\t\t\t\t\tcondensedOriginal[j++] = aggregate + original[i];\n\t\t\t\t\taggregate = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t\ttext = condensedText;\n\t\t\toriginal = condensedOriginal;\n\t\t\tif (aggregate) {\n\t\t\t\ttext.push(aggregate);\n\t\t\t\toriginal.push(aggregate);\n\t\t\t}\n\t\t}\n\t\tvalue.speed && tween.duration(Math.min(0.05 / value.speed * short.length, value.maxDuration || 9999));\n\t\tdata.rtl = rtl;\n\t\tdata.original = original;\n\t\tdata.text = text;\n\t\tdata._props.push(\"text\");\n\t},\n\trender(ratio, data) {\n\t\tif (ratio > 1) {\n\t\t\tratio = 1;\n\t\t} else if (ratio < 0) {\n\t\t\tratio = 0;\n\t\t}\n\t\tif (data.from) {\n\t\t\tratio = 1 - ratio;\n\t\t}\n\t\tlet { text, hasClass, newClass, oldClass, delimiter, target, fillChar, original, rtl } = data,\n\t\t\tl = text.length,\n\t\t\ti = ((rtl ? 1 - ratio : ratio) * l + 0.5) | 0,\n\t\t\tapplyNew, applyOld, str;\n\t\tif (hasClass && ratio) {\n\t\t\tapplyNew = (newClass && i);\n\t\t\tapplyOld = (oldClass && i !== l);\n\t\t\tstr = (applyNew ? \"<span class='\" + newClass + \"'>\" : \"\") + text.slice(0, i).join(delimiter) + (applyNew ? \"</span>\" : \"\") + (applyOld ? \"<span class='\" + oldClass + \"'>\" : \"\") + delimiter + original.slice(i).join(delimiter) + (applyOld ? \"</span>\" : \"\");\n\t\t} else {\n\t\t\tstr = text.slice(0, i).join(delimiter) + delimiter + original.slice(i).join(delimiter);\n\t\t}\n\t\tif (data.svg) { //SVG text elements don't have an \"innerHTML\" in Microsoft browsers.\n\t\t\ttarget.textContent = str;\n\t\t} else {\n\t\t\ttarget.innerHTML = (fillChar === \"&nbsp;\" && ~str.indexOf(\"  \")) ? str.split(\"  \").join(\"&nbsp;&nbsp;\") : str;\n\t\t}\n\t}\n};\n\nTextPlugin.splitInnerHTML = splitInnerHTML;\nTextPlugin.emojiSafeSplit = emojiSafeSplit;\nTextPlugin.getText = getText;\n\n_getGSAP() && gsap.registerPlugin(TextPlugin);\n\nexport { TextPlugin as default };"], "names": ["_trimExp", "emojiExp", "splitInnerHTML", "element", "delimiter", "trim", "preserveSpaces", "unescapedCharCodes", "s", "node", "<PERSON><PERSON><PERSON><PERSON>", "result", "nodeType", "nodeValue", "replace", "push", "emojiSafeSplit", "nodeName", "toLowerCase", "length", "outerHTML", "nextS<PERSON>ling", "splice", "text", "split", "j", "character", "l", "i", "char<PERSON>t", "charCodeAt", "substr", "emoji", "gsap", "_tempDiv", "TextPlugin", "version", "name", "init", "target", "value", "tween", "short", "original", "condensedText", "condensedOriginal", "aggregate", "toUpperCase", "data", "this", "newClass", "oldClass", "rtl", "fillC<PERSON>", "padSpace", "svg", "getBBox", "document", "createElement", "innerHTML", "from", "_from", "hasClass", "type", "speed", "duration", "Math", "min", "maxDuration", "_props", "render", "ratio", "applyNew", "applyOld", "str", "slice", "join", "textContent", "indexOf", "getText", "e", "_getGSAP", "window", "registerPlugin"], "mappings": ";;;;;;;;;6MAUA,IAAIA,EAAW,iBAEFC,EAAW,4gOAmBjB,SAASC,eAAeC,EAASC,EAAWC,EAAMC,EAAgBC,WAE1DC,EADVC,EAAON,EAAQO,WAClBC,EAAS,GACHF,GACgB,IAAlBA,EAAKG,UACRJ,GAAKC,EAAKI,UAAY,IAAIC,QAAQ,QAAS,IACtCR,IACJE,EAAIA,EAAEM,QAAQ,OAAQ,MAEvBH,EAAOI,WAAPJ,EAAeK,eAAeR,EAAGJ,EAAWC,EAAMC,EAAgBC,KACjB,QAAtCE,EAAKQ,SAAW,IAAIC,cAC/BP,EAAOA,EAAOQ,OAAO,IAAM,OAE3BR,EAAOI,KAAKN,EAAKW,WAElBX,EAAOA,EAAKY,gBAERd,MACJC,EAAIG,EAAOQ,OACJX,KACQ,MAAdG,EAAOH,IAAcG,EAAOW,OAAOd,EAAG,EAAG,gBAGpCG,EAgBD,SAASK,eAAeO,EAAMnB,EAAWC,EAAMC,EAAgBC,MACrEgB,GAAQ,GACRlB,IAASkB,EAAOA,EAAKlB,KAAOkB,EAAKlB,OAASkB,EAAKT,QAAQd,EAAU,KAC7DI,GAA2B,KAAdA,SACTmB,EAAKT,QAAQ,KAAM,QAAQA,QAAQ,KAAM,QAAQU,MAAMpB,WAK9DqB,EAAGC,EAHAf,EAAS,GACZgB,EAAIJ,EAAKJ,OACTS,EAAI,EAEEA,EAAID,EAAGC,KAEmB,QADhCF,EAAYH,EAAKM,OAAOD,IACTE,WAAW,IAAgBJ,EAAUI,WAAW,IAAM,OAAoC,OAAxBP,EAAKO,WAAWF,EAAE,IAAgBL,EAAKO,WAAWF,EAAE,IAAM,SAC1IH,IAAMF,EAAKQ,OAAOH,EAAG,IAAIJ,MAAMvB,IAAa,IAAI,IAAM,IAAIkB,QAAU,EACpEO,EAAYH,EAAKQ,OAAOH,EAAGH,GAE3BG,GAAKH,GADLd,EAAOqB,MAAQ,IAGhBrB,EAAOI,KAAKR,EAAqBmB,EAA0B,MAAdA,EAAoB,OAAwB,MAAdA,EAAqB,QAASpB,GAAgC,MAAdoB,GAA2C,MAArBH,EAAKM,OAAOD,EAAE,IAAmC,MAArBL,EAAKM,OAAOD,EAAE,GAAyBF,EAAX,iBAEnMf,EC9ER,IAAIsB,EAAMC,EAIGC,EAAa,CACzBC,QAAQ,SACRC,KAAK,OACLC,mBAAKC,EAAQC,EAAOC,GACD,iBAAXD,IAAwBA,EAAQ,CAACA,MAAMA,QAM7CE,EAAOnB,EAAMoB,EAAUlB,EAAGmB,EAAeC,EAAmBC,EAAWtC,EALpEoB,EAAIW,EAAOtB,SAAS8B,cACvBC,EAAOC,KACLC,EAA4CV,EAA5CU,SAAUC,EAAkCX,EAAlCW,SAAU7C,EAAwBkC,EAAxBlC,eAAgB8C,EAAQZ,EAARY,IACtChD,EAAY4C,EAAK5C,UAAYoC,EAAMpC,WAAa,GAChDiD,EAAWL,EAAKK,SAAWb,EAAMa,WAAab,EAAMc,SAAW,SAAW,OAE3EN,EAAKO,IAAOhB,EAAOiB,UAAkB,SAAN5B,GAAsB,UAANA,KACzC,cAAeW,GAAYS,EAAKO,YAC9B,KAERP,EAAKT,OAASA,EACR,UAAWC,OAIjBG,EAAWzC,eAAeqC,EAAQnC,GAAW,EAAOE,EAAgB0C,EAAKO,MAC5DrB,EAAbA,GAAwBuB,SAASC,cAAc,QACtCC,UAAYnB,EAAMA,MAC3BjB,EAAOrB,eAAegC,EAAU9B,GAAW,EAAOE,EAAgB0C,EAAKO,KACvEP,EAAKY,KAAOnB,EAAMoB,OACbb,EAAKY,OAAQR,GAAUA,GAAOJ,EAAKY,OACvChC,EAAIe,EACJA,EAAWpB,EACXA,EAAOK,GAERoB,EAAKc,YAAcZ,IAAYC,GAC/BH,EAAKE,SAAWE,EAAMD,EAAWD,EACjCF,EAAKG,SAAWC,EAAMF,EAAWC,EAEjCT,GADAd,EAAIe,EAASxB,OAASI,EAAKJ,QACf,EAAIwB,EAAWpB,EACvBK,EAAI,IACPA,GAAKA,IAEQ,IAALA,GACRc,EAAM3B,KAAKsC,MAEO,SAAfb,EAAMuB,KAAiB,KAE1BnB,EAAgB,GAChBC,EAAoB,GACpBC,EAAY,GACPlB,EAJLH,EAAI,EAIQG,EAAIL,EAAKJ,OAAQS,KAC5BpB,EAAIe,EAAKK,MACCe,EAASf,GAClBkB,GAAatC,GAEboC,EAAcnB,GAAKqB,EAAYtC,EAC/BqC,EAAkBpB,KAAOqB,EAAYH,EAASf,GAC9CkB,EAAY,IAGdvB,EAAOqB,EACPD,EAAWE,EACPC,IACHvB,EAAKR,KAAK+B,GACVH,EAAS5B,KAAK+B,IAGhBN,EAAMwB,OAASvB,EAAMwB,SAASC,KAAKC,IAAI,IAAO3B,EAAMwB,MAAQtB,EAAMvB,OAAQqB,EAAM4B,aAAe,OAC/FpB,EAAKI,IAAMA,EACXJ,EAAKL,SAAWA,EAChBK,EAAKzB,KAAOA,EACZyB,EAAKqB,OAAOtD,KAAK,aAlDhBiC,EAAKzB,KAAOyB,EAAKL,SAAW,CAAC,KAoD/B2B,uBAAOC,EAAOvB,GACD,EAARuB,EACHA,EAAQ,EACEA,EAAQ,IAClBA,EAAQ,GAELvB,EAAKY,OACRW,EAAQ,EAAIA,OAKZC,EAAUC,EAAUC,EAHfnD,EAAmFyB,EAAnFzB,KAAMuC,EAA6Ed,EAA7Ec,SAAUZ,EAAmEF,EAAnEE,SAAUC,EAAyDH,EAAzDG,SAAU/C,EAA+C4C,EAA/C5C,UAAWmC,EAAoCS,EAApCT,OAAQc,EAA4BL,EAA5BK,SAAUV,EAAkBK,EAAlBL,SAAUS,EAAQJ,EAARI,IAChFzB,EAAIJ,EAAKJ,OACTS,GAAMwB,EAAM,EAAImB,EAAQA,GAAS5C,EAAI,GAAO,EAK5C+C,EAHGZ,GAAYS,GAEfE,EAAYtB,GAAYvB,IAAMD,IAD9B6C,EAAYtB,GAAYtB,GAEN,gBAAkBsB,EAAW,KAAO,IAAM3B,EAAKoD,MAAM,EAAG/C,GAAGgD,KAAKxE,IAAcoE,EAAW,UAAY,KAAOC,EAAW,gBAAkBtB,EAAW,KAAO,IAAM/C,EAAYuC,EAASgC,MAAM/C,GAAGgD,KAAKxE,IAAcqE,EAAW,UAAY,KAErPlD,EAAKoD,MAAM,EAAG/C,GAAGgD,KAAKxE,GAAaA,EAAYuC,EAASgC,MAAM/C,GAAGgD,KAAKxE,GAEzE4C,EAAKO,IACRhB,EAAOsC,YAAcH,EAErBnC,EAAOoB,UAA0B,WAAbN,IAA0BqB,EAAII,QAAQ,MAASJ,EAAIlD,MAAM,MAAMoD,KAAK,gBAAkBF,IAK7GvC,EAAWjC,eAAiBA,eAC5BiC,EAAWnB,eAAiBA,eAC5BmB,EAAW4C,QDrGJ,SAASA,QAAQC,OACnBjB,EAAOiB,EAAEpE,SACZD,EAAS,MACG,IAAToD,GAAuB,IAATA,GAAuB,KAATA,EAAa,IACd,iBAAnBiB,EAAEH,mBACLG,EAAEH,gBAEJG,EAAIA,EAAEtE,WAAYsE,EAAGA,EAAIA,EAAE3D,YAC/BV,GAAUoE,QAAQC,QAGd,GAAa,IAATjB,GAAuB,IAATA,SACjBiB,EAAEnE,iBAEHF,GCfI,SAAXsE,kBAAiBhD,GAA4B,oBAAZiD,SAA4BjD,EAAOiD,OAAOjD,OAASA,EAAKkD,gBAAkBlD,EAwG5GgD,IAAchD,EAAKkD,eAAehD"}