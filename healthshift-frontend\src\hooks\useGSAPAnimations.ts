import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

// Custom text splitting function
const splitText = (element: HTMLElement) => {
  const text = element.textContent || '';
  const words = text.split(' ');
  element.innerHTML = words.map(word =>
    `<span class="word">${word.split('').map(char =>
      `<span class="char">${char}</span>`
    ).join('')}</span>`
  ).join(' ');
  return element.querySelectorAll('.char');
};

export const useGSAPAnimations = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // Advanced hero text animation with character reveal
      const heroTitle = document.querySelector('.hero-title');
      if (heroTitle) {
        const chars = splitText(heroTitle as HTMLElement);
        gsap.fromTo(chars,
          { opacity: 0, y: 50, rotationX: -90 },
          {
            opacity: 1,
            y: 0,
            rotationX: 0,
            duration: 0.8,
            stagger: 0.02,
            ease: "back.out(1.7)",
            delay: 0.3
          }
        );
      }

      // Morphing badge animation
      gsap.fromTo('.hero-badge',
        {
          opacity: 0,
          scale: 0.8,
          rotationY: 180,
          transformOrigin: "center center"
        },
        {
          opacity: 1,
          scale: 1,
          rotationY: 0,
          duration: 1.2,
          ease: "elastic.out(1, 0.5)"
        }
      );

      // Floating subtitle with wave effect
      gsap.fromTo('.hero-subtitle',
        { opacity: 0, y: 30, skewY: 5 },
        {
          opacity: 1,
          y: 0,
          skewY: 0,
          duration: 1,
          delay: 0.8,
          ease: "power3.out"
        }
      );

      // Button magnetic effect
      const buttons = document.querySelectorAll('.hero-buttons .btn-primary, .hero-buttons .btn-secondary');
      buttons.forEach((button, index) => {
        gsap.fromTo(button,
          {
            opacity: 0,
            y: 40,
            scale: 0.8,
            rotation: index % 2 === 0 ? -5 : 5
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            rotation: 0,
            duration: 0.8,
            delay: 1 + (index * 0.1),
            ease: "back.out(1.7)"
          }
        );
      });

      // Stats cards with 3D flip effect
      gsap.fromTo('.hero-stats .stat-card',
        {
          opacity: 0,
          rotationY: -90,
          transformOrigin: "center center",
          z: -100
        },
        {
          opacity: 1,
          rotationY: 0,
          z: 0,
          duration: 1,
          delay: 1.2,
          stagger: 0.2,
          ease: "power2.out"
        }
      );

      // Feature cards with morphing entrance
      gsap.fromTo('.feature-card',
        {
          opacity: 0,
          y: 100,
          scale: 0.8,
          rotationX: 45,
          transformOrigin: "center bottom"
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          rotationX: 0,
          duration: 1.2,
          stagger: 0.15,
          ease: "power3.out",
          scrollTrigger: {
            trigger: '.features-section',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Parallax effect for feature icons
      gsap.to('.feature-card .icon-container', {
        y: -20,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        stagger: 0.3
      });

      // How it works cards with slide and rotate
      gsap.fromTo('.how-it-works-card',
        {
          opacity: 0,
          x: -100,
          rotation: -10,
          transformOrigin: "center center"
        },
        {
          opacity: 1,
          x: 0,
          rotation: 0,
          duration: 1,
          stagger: 0.2,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: '.how-it-works-section',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Animated step numbers
      gsap.fromTo('.how-it-works-card .bg-accent',
        {
          scale: 0,
          rotation: 180
        },
        {
          scale: 1,
          rotation: 0,
          duration: 0.6,
          stagger: 0.1,
          ease: "back.out(2)",
          scrollTrigger: {
            trigger: '.how-it-works-section',
            start: 'top 70%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // CTA section
      gsap.fromTo('.cta-card', 
        { opacity: 0, scale: 0.95 },
        {
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: '.cta-section',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Subtle parallax effect for background elements
      gsap.to('.parallax-slow', {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: "body",
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

      // Advanced hover animations for cards
      const cards = document.querySelectorAll('.cred-card, .feature-card, .stat-card');
      cards.forEach(card => {
        const cardElement = card as HTMLElement;

        // Create magnetic effect
        cardElement.addEventListener('mouseenter', (e) => {
          gsap.to(cardElement, {
            y: -8,
            scale: 1.03,
            rotationY: 5,
            rotationX: 5,
            transformOrigin: "center center",
            duration: 0.4,
            ease: "power2.out"
          });

          // Animate child elements
          gsap.to(cardElement.querySelector('.icon-container'), {
            scale: 1.1,
            rotation: 10,
            duration: 0.3,
            ease: "back.out(1.7)"
          });
        });

        cardElement.addEventListener('mouseleave', () => {
          gsap.to(cardElement, {
            y: 0,
            scale: 1,
            rotationY: 0,
            rotationX: 0,
            duration: 0.4,
            ease: "power2.out"
          });

          gsap.to(cardElement.querySelector('.icon-container'), {
            scale: 1,
            rotation: 0,
            duration: 0.3,
            ease: "power2.out"
          });
        });

        // Mouse move parallax effect
        cardElement.addEventListener('mousemove', (e) => {
          const rect = cardElement.getBoundingClientRect();
          const x = e.clientX - rect.left - rect.width / 2;
          const y = e.clientY - rect.top - rect.height / 2;

          gsap.to(cardElement, {
            rotationY: x / 10,
            rotationX: -y / 10,
            duration: 0.3,
            ease: "power2.out"
          });
        });
      });

      // Advanced button animations
      const advancedButtons = document.querySelectorAll('.btn-primary, .btn-secondary');
      advancedButtons.forEach(button => {
        const buttonElement = button as HTMLElement;

        buttonElement.addEventListener('mouseenter', () => {
          gsap.to(buttonElement, {
            scale: 1.05,
            y: -2,
            boxShadow: "0 10px 30px rgba(0, 212, 170, 0.3)",
            duration: 0.3,
            ease: "back.out(1.7)"
          });

          // Animate button text
          gsap.to(buttonElement.querySelectorAll('svg'), {
            x: 5,
            rotation: 15,
            duration: 0.3,
            ease: "power2.out"
          });
        });

        buttonElement.addEventListener('mouseleave', () => {
          gsap.to(buttonElement, {
            scale: 1,
            y: 0,
            boxShadow: "0 0 0 rgba(0, 212, 170, 0)",
            duration: 0.3,
            ease: "power2.out"
          });

          gsap.to(buttonElement.querySelectorAll('svg'), {
            x: 0,
            rotation: 0,
            duration: 0.3,
            ease: "power2.out"
          });
        });
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return containerRef;
};

export const useCounterAnimation = (target: number, duration: number = 2) => {
  const elementRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;
    const obj = { value: 0 };

    gsap.to(obj, {
      value: target,
      duration,
      ease: "power2.out",
      onUpdate: () => {
        element.textContent = Math.round(obj.value).toLocaleString();
      },
      scrollTrigger: {
        trigger: element,
        start: 'top 80%',
        toggleActions: 'play none none none'
      }
    });
  }, [target, duration]);

  return elementRef;
};

export const useTextReveal = () => {
  const textRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!textRef.current) return;

    const text = textRef.current;
    const words = text.textContent?.split(' ') || [];
    
    text.innerHTML = words.map(word => 
      `<span class="inline-block">${word}</span>`
    ).join(' ');

    const spans = text.querySelectorAll('span');

    gsap.fromTo(spans, 
      { opacity: 0, y: 20 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: text,
          start: 'top 80%',
          toggleActions: 'play none none none'
        }
      }
    );
  }, []);

  return textRef;
};
