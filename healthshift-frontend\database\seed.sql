-- Sample data for development and testing
-- Note: In production, users would be created through the auth system

-- Insert sample users (these would normally be created via Supabase Auth)
-- For development, we'll assume these auth users exist
INSERT INTO users (id, email, first_name, last_name, role) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '<PERSON>', '<PERSON>', 'professional'),
    ('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '<PERSON>', '<PERSON>', 'professional'),
    ('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '<PERSON>', '<PERSON>', 'professional'),
    ('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', 'City General', 'Hospital', 'facility'),
    ('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'Sunnydale', 'Clinic', 'facility'),
    ('550e8400-e29b-41d4-a716-446655440006', '<EMAIL>', 'Greenwood', 'Nursing Home', 'facility');

-- Insert healthcare professionals
INSERT INTO healthcare_professionals (user_id, license_number, specialization, experience, hourly_rate, certifications, bio, rating, completed_shifts) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'RN123456', 'registered_nurse', 5, 45.00, ARRAY['BLS', 'ACLS', 'PALS'], 'Experienced ICU nurse with 5 years of critical care experience.', 4.8, 127),
    ('550e8400-e29b-41d4-a716-446655440002', 'LPN789012', 'licensed_practical_nurse', 3, 32.00, ARRAY['BLS', 'IV Therapy'], 'Dedicated LPN specializing in geriatric care and medication administration.', 4.6, 89),
    ('550e8400-e29b-41d4-a716-446655440003', 'CNA345678', 'certified_nursing_assistant', 2, 22.00, ARRAY['BLS', 'First Aid'], 'Compassionate CNA with experience in long-term care and rehabilitation.', 4.7, 156);

-- Insert facilities
INSERT INTO facilities (user_id, facility_name, facility_type, address, contact_phone, license_number, rating, total_shifts_posted) VALUES
    ('550e8400-e29b-41d4-a716-446655440004', 'City General Hospital', 'hospital', 
     '{"street": "123 Medical Center Dr", "city": "Springfield", "state": "IL", "zipCode": "62701", "country": "USA"}', 
     '******-0123', 'HOSP001', 4.5, 234),
    ('550e8400-e29b-41d4-a716-446655440005', 'Sunnydale Family Clinic', 'clinic', 
     '{"street": "456 Health Ave", "city": "Springfield", "state": "IL", "zipCode": "62702", "country": "USA"}', 
     '******-0456', 'CLIN002', 4.3, 89),
    ('550e8400-e29b-41d4-a716-446655440006', 'Greenwood Nursing Home', 'nursing_home', 
     '{"street": "789 Care Blvd", "city": "Springfield", "state": "IL", "zipCode": "62703", "country": "USA"}', 
     '******-0789', 'NH003', 4.2, 156);

-- Insert sample shifts
INSERT INTO shifts (facility_id, title, description, specialization, start_date_time, end_date_time, hourly_rate, status, requirements) VALUES
    ((SELECT id FROM facilities WHERE facility_name = 'City General Hospital'), 
     'ICU Night Shift', 
     'Critical care nursing position for night shift in our 24-bed ICU. Experience with ventilators and cardiac monitoring required.',
     'registered_nurse', 
     '2024-01-15 19:00:00+00', 
     '2024-01-16 07:00:00+00', 
     48.00, 
     'open', 
     ARRAY['ICU Experience', 'ACLS Certification', 'Ventilator Training']),
    
    ((SELECT id FROM facilities WHERE facility_name = 'Sunnydale Family Clinic'), 
     'Weekend Clinic Coverage', 
     'General nursing duties for weekend clinic coverage. Assist with patient care and medication administration.',
     'licensed_practical_nurse', 
     '2024-01-13 08:00:00+00', 
     '2024-01-13 16:00:00+00', 
     35.00, 
     'open', 
     ARRAY['Medication Administration', 'Patient Assessment']),
    
    ((SELECT id FROM facilities WHERE facility_name = 'Greenwood Nursing Home'), 
     'Day Shift CNA', 
     'Provide direct patient care including ADLs, vital signs, and mobility assistance for elderly residents.',
     'certified_nursing_assistant', 
     '2024-01-14 07:00:00+00', 
     '2024-01-14 15:00:00+00', 
     24.00, 
     'open', 
     ARRAY['Geriatric Experience', 'ADL Assistance', 'Mobility Training']),
    
    ((SELECT id FROM facilities WHERE facility_name = 'City General Hospital'), 
     'Emergency Department RN', 
     'Fast-paced emergency department position. Triage experience preferred.',
     'registered_nurse', 
     '2024-01-16 15:00:00+00', 
     '2024-01-16 23:00:00+00', 
     52.00, 
     'booked', 
     ARRAY['Triage Experience', 'ACLS', 'Trauma Experience'],
     (SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440001')),
    
    ((SELECT id FROM facilities WHERE facility_name = 'Sunnydale Family Clinic'), 
     'Flu Clinic Support', 
     'Assist with flu vaccination clinic. Help with patient flow and documentation.',
     'licensed_practical_nurse', 
     '2024-01-12 09:00:00+00', 
     '2024-01-12 17:00:00+00', 
     30.00, 
     'completed', 
     ARRAY['Vaccination Experience', 'Documentation Skills']);

-- Insert sample availability for healthcare professionals
INSERT INTO availability (professional_id, day_of_week, start_time, end_time) VALUES
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440001'), 1, '07:00', '19:00'), -- Monday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440001'), 2, '07:00', '19:00'), -- Tuesday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440001'), 3, '07:00', '19:00'), -- Wednesday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440001'), 6, '19:00', '07:00'), -- Saturday nights
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440001'), 0, '19:00', '07:00'), -- Sunday nights
    
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440002'), 1, '08:00', '16:00'), -- Monday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440002'), 2, '08:00', '16:00'), -- Tuesday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440002'), 3, '08:00', '16:00'), -- Wednesday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440002'), 4, '08:00', '16:00'), -- Thursday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440002'), 5, '08:00', '16:00'), -- Friday
    
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440003'), 0, '06:00', '14:00'), -- Sunday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440003'), 1, '06:00', '14:00'), -- Monday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440003'), 2, '06:00', '14:00'), -- Tuesday
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440003'), 5, '14:00', '22:00'), -- Friday evening
    ((SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440003'), 6, '14:00', '22:00'); -- Saturday evening

-- Insert sample bookings
INSERT INTO bookings (shift_id, professional_id, status, notes) VALUES
    ((SELECT id FROM shifts WHERE title = 'Emergency Department RN'), 
     (SELECT id FROM healthcare_professionals WHERE user_id = '550e8400-e29b-41d4-a716-446655440001'), 
     'confirmed', 
     'Looking forward to working in the ED. I have 2 years of trauma experience.');

-- Insert sample notifications
INSERT INTO notifications (user_id, type, title, message, read, related_id) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'shift_confirmed', 'Shift Confirmed', 'Your booking for Emergency Department RN shift has been confirmed.', false, 
     (SELECT id FROM shifts WHERE title = 'Emergency Department RN')),
    ('550e8400-e29b-41d4-a716-446655440004', 'shift_booked', 'New Booking', 'Sarah Johnson has booked your Emergency Department RN shift.', false, 
     (SELECT id FROM shifts WHERE title = 'Emergency Department RN')),
    ('550e8400-e29b-41d4-a716-446655440002', 'new_shift_available', 'New Shift Available', 'A new LPN shift matching your specialization is available at Sunnydale Family Clinic.', false, 
     (SELECT id FROM shifts WHERE title = 'Weekend Clinic Coverage')),
    ('550e8400-e29b-41d4-a716-446655440003', 'new_shift_available', 'New Shift Available', 'A new CNA shift is available at Greenwood Nursing Home.', true, 
     (SELECT id FROM shifts WHERE title = 'Day Shift CNA'));
