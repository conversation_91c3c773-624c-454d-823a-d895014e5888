// DO NOT EDIT. This file is generated with `npm run build:space-accessors`.

/** Proxy used for space accessors */
export type SpaceAccessor = Record<string, number> & number[];

declare class SpaceAccessors {
	a98rgb: SpaceAccessor;
	a98rgb_linear: SpaceAccessor;
	acescc: SpaceAccessor;
	acescg: SpaceAccessor;
	cam16_jmh: SpaceAccessor;
	hct: SpaceAccessor;
	hpluv: SpaceAccessor;
	hsl: SpaceAccessor;
	hsluv: SpaceAccessor;
	hsv: SpaceAccessor;
	hwb: SpaceAccessor;
	ictcp: SpaceAccessor;
	jzazbz: SpaceAccessor;
	jzczhz: SpaceAccessor;
	lab: SpaceAccessor;
	lab_d65: SpaceAccessor;
	lch: SpaceAccessor;
	lchuv: SpaceAccessor;
	luv: SpaceAccessor;
	oklab: SpaceAccessor;
	oklch: SpaceAccessor;
	p3: SpaceAccessor;
	p3_linear: SpaceAccessor;
	prophoto: SpaceAccessor;
	prophoto_linear: SpaceAccessor;
	rec2020: SpaceAccessor;
	rec2020_linear: SpaceAccessor;
	rec2100hlg: SpaceAccessor;
	rec2100pq: SpaceAccessor;
	srgb: SpaceAccessor;
	srgb_linear: SpaceAccessor;
	xyz: SpaceAccessor;
	xyz_abs_d65: SpaceAccessor;
	xyz_d50: SpaceAccessor;
	xyz_d65: SpaceAccessor;
	a: number;
	az: number;
	b: number;
	bz: number;
	c: number;
	cp: number;
	ct: number;
	cz: number;
	g: number;
	h: number;
	hz: number;
	i: number;
	j: number;
	jz: number;
	l: number;
	m: number;
	r: number;
	s: number;
	t: number;
	u: number;
	v: number;
	w: number;
	x: number;
	y: number;
	z: number;
}

export default SpaceAccessors;
