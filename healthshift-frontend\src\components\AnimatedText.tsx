import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

interface AnimatedTextProps {
  text: string;
  className?: string;
  animationType?: 'typewriter' | 'reveal' | 'wave' | 'glitch';
  delay?: number;
  trigger?: string;
}

const AnimatedText: React.FC<AnimatedTextProps> = ({ 
  text, 
  className = '', 
  animationType = 'reveal',
  delay = 0,
  trigger
}) => {
  const textRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (!textRef.current) return;

    const element = textRef.current;
    const chars = text.split('').map(char => 
      `<span class="char inline-block">${char === ' ' ? '&nbsp;' : char}</span>`
    ).join('');
    
    element.innerHTML = chars;
    const charElements = element.querySelectorAll('.char');

    let animation;

    switch (animationType) {
      case 'typewriter':
        gsap.set(charElements, { opacity: 0 });
        animation = gsap.to(charElements, {
          opacity: 1,
          duration: 0.05,
          stagger: 0.05,
          ease: "none",
          delay
        });
        break;

      case 'wave':
        gsap.set(charElements, { y: 50, opacity: 0 });
        animation = gsap.to(charElements, {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.03,
          ease: "back.out(1.7)",
          delay
        });
        break;

      case 'glitch':
        gsap.set(charElements, { 
          x: () => Math.random() * 100 - 50,
          y: () => Math.random() * 100 - 50,
          opacity: 0,
          scale: () => Math.random() * 2 + 0.5
        });
        animation = gsap.to(charElements, {
          x: 0,
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.02,
          ease: "power3.out",
          delay
        });
        break;

      default: // reveal
        gsap.set(charElements, { 
          y: 100, 
          opacity: 0,
          rotationX: -90
        });
        animation = gsap.to(charElements, {
          y: 0,
          opacity: 1,
          rotationX: 0,
          duration: 0.8,
          stagger: 0.02,
          ease: "power3.out",
          delay
        });
    }

    if (trigger) {
      ScrollTrigger.create({
        trigger: trigger,
        start: 'top 80%',
        onEnter: () => animation.play(),
        onLeave: () => animation.reverse(),
        onEnterBack: () => animation.play(),
        onLeaveBack: () => animation.reverse()
      });
      animation.pause();
    }

    return () => {
      animation.kill();
      ScrollTrigger.getAll().forEach(st => st.kill());
    };
  }, [text, animationType, delay, trigger]);

  return (
    <span 
      ref={textRef} 
      className={className}
      style={{ perspective: '1000px' }}
    />
  );
};

export default AnimatedText;
