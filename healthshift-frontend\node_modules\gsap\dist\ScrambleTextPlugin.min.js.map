{"version": 3, "file": "ScrambleTextPlugin.min.js", "sources": ["../src/utils/strings.js", "../src/ScrambleTextPlugin.js"], "sourcesContent": ["/*!\n * strings: 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _trimExp = /(?:^\\s+|\\s+$)/g;\n\nexport const emojiExp = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF](?:[\\u200D\\uFE0F][\\uD800-\\uDBFF][\\uDC00-\\uDFFF]){2,}|\\uD83D\\uDC69(?:\\u200D(?:(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC67|(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC66)|\\uD83C[\\uDFFB-\\uDFFF])|\\uD83D\\uDC69\\u200D(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC66\\u200D\\uD83D\\uDC66|\\uD83D\\uDC69\\u200D(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC67\\u200D(?:\\uD83D[\\uDC66\\uDC67])|\\uD83C\\uDFF3\\uFE0F\\u200D\\uD83C\\uDF08|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3D\\uDD3E\\uDDD6-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2642\\u2640]\\uFE0F|\\uD83D\\uDC69(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDD27\\uDCBC\\uDD2C\\uDE80\\uDE92])|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC6F\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3C-\\uDD3E\\uDDD6-\\uDDDF])\\u200D[\\u2640\\u2642]\\uFE0F|\\uD83C\\uDDFD\\uD83C\\uDDF0|\\uD83C\\uDDF6\\uD83C\\uDDE6|\\uD83C\\uDDF4\\uD83C\\uDDF2|\\uD83C\\uDDE9(?:\\uD83C[\\uDDEA\\uDDEC\\uDDEF\\uDDF0\\uDDF2\\uDDF4\\uDDFF])|\\uD83C\\uDDF7(?:\\uD83C[\\uDDEA\\uDDF4\\uDDF8\\uDDFA\\uDDFC])|\\uD83C\\uDDE8(?:\\uD83C[\\uDDE6\\uDDE8\\uDDE9\\uDDEB-\\uDDEE\\uDDF0-\\uDDF5\\uDDF7\\uDDFA-\\uDDFF])|(?:\\u26F9|\\uD83C[\\uDFCC\\uDFCB]|\\uD83D\\uDD75)(?:\\uFE0F\\u200D[\\u2640\\u2642]|(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2640\\u2642])\\uFE0F|(?:\\uD83D\\uDC41\\uFE0F\\u200D\\uD83D\\uDDE8|\\uD83D\\uDC69(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2695\\u2696\\u2708]|\\uD83D\\uDC69\\u200D[\\u2695\\u2696\\u2708]|\\uD83D\\uDC68(?:(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2695\\u2696\\u2708]|\\u200D[\\u2695\\u2696\\u2708]))\\uFE0F|\\uD83C\\uDDF2(?:\\uD83C[\\uDDE6\\uDDE8-\\uDDED\\uDDF0-\\uDDFF])|\\uD83D\\uDC69\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92]|\\u2764\\uFE0F\\u200D(?:\\uD83D\\uDC8B\\u200D(?:\\uD83D[\\uDC68\\uDC69])|\\uD83D[\\uDC68\\uDC69]))|\\uD83C\\uDDF1(?:\\uD83C[\\uDDE6-\\uDDE8\\uDDEE\\uDDF0\\uDDF7-\\uDDFB\\uDDFE])|\\uD83C\\uDDEF(?:\\uD83C[\\uDDEA\\uDDF2\\uDDF4\\uDDF5])|\\uD83C\\uDDED(?:\\uD83C[\\uDDF0\\uDDF2\\uDDF3\\uDDF7\\uDDF9\\uDDFA])|\\uD83C\\uDDEB(?:\\uD83C[\\uDDEE-\\uDDF0\\uDDF2\\uDDF4\\uDDF7])|[#\\*0-9]\\uFE0F\\u20E3|\\uD83C\\uDDE7(?:\\uD83C[\\uDDE6\\uDDE7\\uDDE9-\\uDDEF\\uDDF1-\\uDDF4\\uDDF6-\\uDDF9\\uDDFB\\uDDFC\\uDDFE\\uDDFF])|\\uD83C\\uDDE6(?:\\uD83C[\\uDDE8-\\uDDEC\\uDDEE\\uDDF1\\uDDF2\\uDDF4\\uDDF6-\\uDDFA\\uDDFC\\uDDFD\\uDDFF])|\\uD83C\\uDDFF(?:\\uD83C[\\uDDE6\\uDDF2\\uDDFC])|\\uD83C\\uDDF5(?:\\uD83C[\\uDDE6\\uDDEA-\\uDDED\\uDDF0-\\uDDF3\\uDDF7-\\uDDF9\\uDDFC\\uDDFE])|\\uD83C\\uDDFB(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA\\uDDEC\\uDDEE\\uDDF3\\uDDFA])|\\uD83C\\uDDF3(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA-\\uDDEC\\uDDEE\\uDDF1\\uDDF4\\uDDF5\\uDDF7\\uDDFA\\uDDFF])|\\uD83C\\uDFF4\\uDB40\\uDC67\\uDB40\\uDC62(?:\\uDB40\\uDC77\\uDB40\\uDC6C\\uDB40\\uDC73|\\uDB40\\uDC73\\uDB40\\uDC63\\uDB40\\uDC74|\\uDB40\\uDC65\\uDB40\\uDC6E\\uDB40\\uDC67)\\uDB40\\uDC7F|\\uD83D\\uDC68(?:\\u200D(?:\\u2764\\uFE0F\\u200D(?:\\uD83D\\uDC8B\\u200D)?\\uD83D\\uDC68|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC66\\u200D\\uD83D\\uDC66|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC67\\u200D(?:\\uD83D[\\uDC66\\uDC67])|\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92])|(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92]))|\\uD83C\\uDDF8(?:\\uD83C[\\uDDE6-\\uDDEA\\uDDEC-\\uDDF4\\uDDF7-\\uDDF9\\uDDFB\\uDDFD-\\uDDFF])|\\uD83C\\uDDF0(?:\\uD83C[\\uDDEA\\uDDEC-\\uDDEE\\uDDF2\\uDDF3\\uDDF5\\uDDF7\\uDDFC\\uDDFE\\uDDFF])|\\uD83C\\uDDFE(?:\\uD83C[\\uDDEA\\uDDF9])|\\uD83C\\uDDEE(?:\\uD83C[\\uDDE8-\\uDDEA\\uDDF1-\\uDDF4\\uDDF6-\\uDDF9])|\\uD83C\\uDDF9(?:\\uD83C[\\uDDE6\\uDDE8\\uDDE9\\uDDEB-\\uDDED\\uDDEF-\\uDDF4\\uDDF7\\uDDF9\\uDDFB\\uDDFC\\uDDFF])|\\uD83C\\uDDEC(?:\\uD83C[\\uDDE6\\uDDE7\\uDDE9-\\uDDEE\\uDDF1-\\uDDF3\\uDDF5-\\uDDFA\\uDDFC\\uDDFE])|\\uD83C\\uDDFA(?:\\uD83C[\\uDDE6\\uDDEC\\uDDF2\\uDDF3\\uDDF8\\uDDFE\\uDDFF])|\\uD83C\\uDDEA(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA\\uDDEC\\uDDED\\uDDF7-\\uDDFA])|\\uD83C\\uDDFC(?:\\uD83C[\\uDDEB\\uDDF8])|(?:\\u26F9|\\uD83C[\\uDFCB\\uDFCC]|\\uD83D\\uDD75)(?:\\uD83C[\\uDFFB-\\uDFFF])|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3D\\uDD3E\\uDDD6-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])|(?:[\\u261D\\u270A-\\u270D]|\\uD83C[\\uDF85\\uDFC2\\uDFC7]|\\uD83D[\\uDC42\\uDC43\\uDC46-\\uDC50\\uDC66\\uDC67\\uDC70\\uDC72\\uDC74-\\uDC76\\uDC78\\uDC7C\\uDC83\\uDC85\\uDCAA\\uDD74\\uDD7A\\uDD90\\uDD95\\uDD96\\uDE4C\\uDE4F\\uDEC0\\uDECC]|\\uD83E[\\uDD18-\\uDD1C\\uDD1E\\uDD1F\\uDD30-\\uDD36\\uDDD1-\\uDDD5])(?:\\uD83C[\\uDFFB-\\uDFFF])|\\uD83D\\uDC68(?:\\u200D(?:(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC67|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC66)|\\uD83C[\\uDFFB-\\uDFFF])|(?:[\\u261D\\u26F9\\u270A-\\u270D]|\\uD83C[\\uDF85\\uDFC2-\\uDFC4\\uDFC7\\uDFCA-\\uDFCC]|\\uD83D[\\uDC42\\uDC43\\uDC46-\\uDC50\\uDC66-\\uDC69\\uDC6E\\uDC70-\\uDC78\\uDC7C\\uDC81-\\uDC83\\uDC85-\\uDC87\\uDCAA\\uDD74\\uDD75\\uDD7A\\uDD90\\uDD95\\uDD96\\uDE45-\\uDE47\\uDE4B-\\uDE4F\\uDEA3\\uDEB4-\\uDEB6\\uDEC0\\uDECC]|\\uD83E[\\uDD18-\\uDD1C\\uDD1E\\uDD1F\\uDD26\\uDD30-\\uDD39\\uDD3D\\uDD3E\\uDDD1-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])?|(?:[\\u231A\\u231B\\u23E9-\\u23EC\\u23F0\\u23F3\\u25FD\\u25FE\\u2614\\u2615\\u2648-\\u2653\\u267F\\u2693\\u26A1\\u26AA\\u26AB\\u26BD\\u26BE\\u26C4\\u26C5\\u26CE\\u26D4\\u26EA\\u26F2\\u26F3\\u26F5\\u26FA\\u26FD\\u2705\\u270A\\u270B\\u2728\\u274C\\u274E\\u2753-\\u2755\\u2757\\u2795-\\u2797\\u27B0\\u27BF\\u2B1B\\u2B1C\\u2B50\\u2B55]|\\uD83C[\\uDC04\\uDCCF\\uDD8E\\uDD91-\\uDD9A\\uDDE6-\\uDDFF\\uDE01\\uDE1A\\uDE2F\\uDE32-\\uDE36\\uDE38-\\uDE3A\\uDE50\\uDE51\\uDF00-\\uDF20\\uDF2D-\\uDF35\\uDF37-\\uDF7C\\uDF7E-\\uDF93\\uDFA0-\\uDFCA\\uDFCF-\\uDFD3\\uDFE0-\\uDFF0\\uDFF4\\uDFF8-\\uDFFF]|\\uD83D[\\uDC00-\\uDC3E\\uDC40\\uDC42-\\uDCFC\\uDCFF-\\uDD3D\\uDD4B-\\uDD4E\\uDD50-\\uDD67\\uDD7A\\uDD95\\uDD96\\uDDA4\\uDDFB-\\uDE4F\\uDE80-\\uDEC5\\uDECC\\uDED0-\\uDED2\\uDEEB\\uDEEC\\uDEF4-\\uDEF8]|\\uD83E[\\uDD10-\\uDD3A\\uDD3C-\\uDD3E\\uDD40-\\uDD45\\uDD47-\\uDD4C\\uDD50-\\uDD6B\\uDD80-\\uDD97\\uDDC0\\uDDD0-\\uDDE6])|(?:[#\\*0-9\\xA9\\xAE\\u203C\\u2049\\u2122\\u2139\\u2194-\\u2199\\u21A9\\u21AA\\u231A\\u231B\\u2328\\u23CF\\u23E9-\\u23F3\\u23F8-\\u23FA\\u24C2\\u25AA\\u25AB\\u25B6\\u25C0\\u25FB-\\u25FE\\u2600-\\u2604\\u260E\\u2611\\u2614\\u2615\\u2618\\u261D\\u2620\\u2622\\u2623\\u2626\\u262A\\u262E\\u262F\\u2638-\\u263A\\u2640\\u2642\\u2648-\\u2653\\u2660\\u2663\\u2665\\u2666\\u2668\\u267B\\u267F\\u2692-\\u2697\\u2699\\u269B\\u269C\\u26A0\\u26A1\\u26AA\\u26AB\\u26B0\\u26B1\\u26BD\\u26BE\\u26C4\\u26C5\\u26C8\\u26CE\\u26CF\\u26D1\\u26D3\\u26D4\\u26E9\\u26EA\\u26F0-\\u26F5\\u26F7-\\u26FA\\u26FD\\u2702\\u2705\\u2708-\\u270D\\u270F\\u2712\\u2714\\u2716\\u271D\\u2721\\u2728\\u2733\\u2734\\u2744\\u2747\\u274C\\u274E\\u2753-\\u2755\\u2757\\u2763\\u2764\\u2795-\\u2797\\u27A1\\u27B0\\u27BF\\u2934\\u2935\\u2B05-\\u2B07\\u2B1B\\u2B1C\\u2B50\\u2B55\\u3030\\u303D\\u3297\\u3299]|\\uD83C[\\uDC04\\uDCCF\\uDD70\\uDD71\\uDD7E\\uDD7F\\uDD8E\\uDD91-\\uDD9A\\uDDE6-\\uDDFF\\uDE01\\uDE02\\uDE1A\\uDE2F\\uDE32-\\uDE3A\\uDE50\\uDE51\\uDF00-\\uDF21\\uDF24-\\uDF93\\uDF96\\uDF97\\uDF99-\\uDF9B\\uDF9E-\\uDFF0\\uDFF3-\\uDFF5\\uDFF7-\\uDFFF]|\\uD83D[\\uDC00-\\uDCFD\\uDCFF-\\uDD3D\\uDD49-\\uDD4E\\uDD50-\\uDD67\\uDD6F\\uDD70\\uDD73-\\uDD7A\\uDD87\\uDD8A-\\uDD8D\\uDD90\\uDD95\\uDD96\\uDDA4\\uDDA5\\uDDA8\\uDDB1\\uDDB2\\uDDBC\\uDDC2-\\uDDC4\\uDDD1-\\uDDD3\\uDDDC-\\uDDDE\\uDDE1\\uDDE3\\uDDE8\\uDDEF\\uDDF3\\uDDFA-\\uDE4F\\uDE80-\\uDEC5\\uDECB-\\uDED2\\uDEE0-\\uDEE5\\uDEE9\\uDEEB\\uDEEC\\uDEF0\\uDEF3-\\uDEF8]|\\uD83E[\\uDD10-\\uDD3A\\uDD3C-\\uDD3E\\uDD40-\\uDD45\\uDD47-\\uDD4C\\uDD50-\\uDD6B\\uDD80-\\uDD97\\uDDC0\\uDDD0-\\uDDE6])\\uFE0F)/;\n\nexport function getText(e) {\n\tlet type = e.nodeType,\n\t\tresult = \"\";\n\tif (type === 1 || type === 9 || type === 11) {\n\t\tif (typeof(e.textContent) === \"string\") {\n\t\t\treturn e.textContent;\n\t\t} else {\n\t\t\tfor (e = e.firstChild; e; e = e.nextSibling ) {\n\t\t\t\tresult += getText(e);\n\t\t\t}\n\t\t}\n\t} else if (type === 3 || type === 4) {\n\t\treturn e.nodeValue;\n\t}\n\treturn result;\n}\n\nexport function splitInnerHTML(element, delimiter, trim, preserveSpaces, unescapedCharCodes) {\n\tlet node = element.firstChild,\n\t\tresult = [], s;\n\twhile (node) {\n\t\tif (node.nodeType === 3) {\n\t\t\ts = (node.nodeValue + \"\").replace(/^\\n+/g, \"\");\n\t\t\tif (!preserveSpaces) {\n\t\t\t\ts = s.replace(/\\s+/g, \" \");\n\t\t\t}\n\t\t\tresult.push(...emojiSafeSplit(s, delimiter, trim, preserveSpaces, unescapedCharCodes));\n\t\t} else if ((node.nodeName + \"\").toLowerCase() === \"br\") {\n\t\t\tresult[result.length-1] += \"<br>\";\n\t\t} else {\n\t\t\tresult.push(node.outerHTML);\n\t\t}\n\t\tnode = node.nextSibling;\n\t}\n\tif (!unescapedCharCodes) {\n\t\ts = result.length;\n\t\twhile (s--) {\n\t\t\tresult[s] === \"&\" && result.splice(s, 1, \"&amp;\");\n\t\t}\n\t}\n\treturn result;\n}\n\n/*\n//smaller kb version that only handles the simpler emoji's, which is often perfectly adequate.\n\nlet _emoji = \"[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2694-\\u2697]|\\uD83E[\\uDD10-\\uDD5D]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]\",\n\t_emojiExp = new RegExp(_emoji),\n\t_emojiAndCharsExp = new RegExp(_emoji + \"|.\", \"g\"),\n\t_emojiSafeSplit = (text, delimiter, trim) => {\n\t\tif (trim) {\n\t\t\ttext = text.replace(_trimExp, \"\");\n\t\t}\n\t\treturn ((delimiter === \"\" || !delimiter) && _emojiExp.test(text)) ? text.match(_emojiAndCharsExp) : text.split(delimiter || \"\");\n\t};\n */\nexport function emojiSafeSplit(text, delimiter, trim, preserveSpaces, unescapedCharCodes) {\n\ttext += \"\"; // make sure it's cast as a string. Someone may pass in a number.\n\ttrim && (text = text.trim ? text.trim() : text.replace(_trimExp, \"\")); // IE9 and earlier compatibility\n\tif (delimiter && delimiter !== \"\") {\n\t\treturn text.replace(/>/g, \"&gt;\").replace(/</g, \"&lt;\").split(delimiter);\n\t}\n\tlet result = [],\n\t\tl = text.length,\n\t\ti = 0,\n\t\tj, character;\n\tfor (; i < l; i++) {\n\t\tcharacter = text.charAt(i);\n\t\tif ((character.charCodeAt(0) >= 0xD800 && character.charCodeAt(0) <= 0xDBFF) || (text.charCodeAt(i+1) >= 0xFE00 && text.charCodeAt(i+1) <= 0xFE0F)) { //special emoji characters use 2 or 4 unicode characters that we must keep together.\n\t\t\tj = ((text.substr(i, 12).split(emojiExp) || [])[1] || \"\").length || 2;\n\t\t\tcharacter = text.substr(i, j);\n\t\t\tresult.emoji = 1;\n\t\t\ti += j - 1;\n\t\t}\n\t\tresult.push(unescapedCharCodes ? character : character === \">\" ? \"&gt;\" : (character === \"<\") ? \"&lt;\" : preserveSpaces && character === \" \" && (text.charAt(i-1) === \" \" || text.charAt(i+1) === \" \") ? \"&nbsp;\" : character);\n\t}\n\treturn result;\n}", "/*!\n * ScrambleTextPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { emojiSafeSplit, getText } from \"./utils/strings.js\";\n\nclass CharSet {\n\tconstructor(chars) {\n\t\tthis.chars = emojiSafeSplit(chars);\n\t\tthis.sets = [];\n\t\tthis.length = 50;\n\t\tfor (let i = 0; i < 20; i++) {\n\t\t\tthis.sets[i] = _scrambleText(80, this.chars); //we create 20 strings that are 80 characters long, randomly chosen and pack them into an array. We then randomly choose the scrambled text from this array in order to greatly improve efficiency compared to creating new randomized text from scratch each and every time it's needed. This is a simple lookup whereas the other technique requires looping through as many times as there are characters needed, and calling Math.random() each time through the loop, building the string, etc.\n\t\t}\n\t}\n\tgrow(newLength) { //if we encounter a tween that has more than 80 characters, we'll need to add to the character sets accordingly. Once it's cached, it'll only need to grow again if we exceed that new length. Again, this is an efficiency tactic.\n\t\tfor (let i = 0; i < 20; i++) {\n\t\t\tthis.sets[i] += _scrambleText(newLength - this.length, this.chars);\n\t\t}\n\t\tthis.length = newLength;\n\t}\n}\n\nlet gsap, _coreInitted,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_bonusValidated = 1, //<name>ScrambleTextPlugin</name>\n\t_spacesExp = /\\s+/g,\n\t_scrambleText = (length, chars) => {\n\t\tlet l = chars.length,\n\t\t\ts = \"\";\n\t\twhile (--length > -1) {\n\t\t\ts += chars[ ~~(Math.random() * l) ];\n\t\t}\n\t\treturn s;\n\t},\n\t_upper = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\",\n\t_lower = _upper.toLowerCase(),\n\t_charsLookup = {\n\t\tupperCase: new CharSet(_upper),\n\t\tlowerCase: new CharSet(_lower),\n\t\tupperAndLowerCase: new CharSet(_upper + _lower)\n\t},\n\t_initCore = () => {\n\t\t_coreInitted = gsap = _getGSAP();\n\t};\n\n\nexport const ScrambleTextPlugin = {\n\tversion:\"3.13.0\",\n\tname:\"scrambleText\",\n\tregister(core, Plugin, propTween) {\n\t\tgsap = core;\n\t\t_initCore();\n\t},\n\tinit(target, value, tween, index, targets) {\n\t\t_coreInitted ||\t_initCore();\n\t\tthis.prop = (\"innerHTML\" in target) ? \"innerHTML\" : (\"textContent\" in target) ? \"textContent\" : 0; // SVG text in IE doesn't have innerHTML, but it does have textContent.\n\t\tif (!this.prop) {\n\t\t\treturn;\n\t\t}\n\t\tthis.target = target;\n\t\tif (typeof(value) !== \"object\") {\n\t\t\tvalue = {text:value};\n\t\t}\n\t\tlet text = value.text || value.value || \"\",\n\t\t\ttrim = (value.trim !== false),\n\t\t\tdata = this,\n\t\t\tdelim, maxLength, charset, splitByChars;\n\t\tdata.delimiter = delim = value.delimiter || \"\";\n\t\tdata.original = emojiSafeSplit(getText(target).replace(_spacesExp, \" \").split(\"&nbsp;\").join(\"\"), delim, trim);\n\t\tif (text === \"{original}\" || text === true || text == null) {\n\t\t\ttext = data.original.join(delim);\n\t\t}\n\t\tdata.text = emojiSafeSplit((text || \"\").replace(_spacesExp, \" \"), delim, trim);\n\t\tdata.hasClass = !!(value.newClass || value.oldClass);\n\t\tdata.newClass = value.newClass;\n\t\tdata.oldClass = value.oldClass;\n\t\tsplitByChars = (delim === \"\");\n\t\tdata.textHasEmoji = splitByChars && !!data.text.emoji;\n\t\tdata.charsHaveEmoji = !!value.chars && !!emojiSafeSplit(value.chars).emoji;\n\t\tdata.length = splitByChars ? data.original.length : data.original.join(delim).length;\n\t\tdata.lengthDif = (splitByChars ? data.text.length : data.text.join(delim).length) - data.length;\n\t\tdata.fillChar = value.fillChar || (value.chars && ~value.chars.indexOf(\" \")) ? \"&nbsp;\" : \"\";\n\t\tdata.charSet = charset = _charsLookup[(value.chars || \"upperCase\")] || new CharSet(value.chars);\n\t\tdata.speed = 0.05 / (value.speed || 1);\n\t\tdata.prevScrambleTime = 0;\n\t\tdata.setIndex = (Math.random() * 20) | 0;\n\t\tmaxLength = data.length + Math.max(data.lengthDif, 0);\n\t\tif (maxLength > charset.length) {\n\t\t\tcharset.grow(maxLength);\n\t\t}\n\t\tdata.chars = charset.sets[data.setIndex];\n\t\tdata.revealDelay = value.revealDelay || 0;\n\t\tdata.tweenLength = (value.tweenLength !== false);\n\t\tdata.tween = tween;\n\t\tdata.rightToLeft = !!value.rightToLeft;\n\t\tdata._props.push(\"scrambleText\", \"text\");\n\t\treturn _bonusValidated;\n\t},\n\trender(ratio, data) {\n\t\tlet { target, prop, text, delimiter, tween, prevScrambleTime, revealDelay, setIndex, chars, charSet, length, textHasEmoji, charsHaveEmoji, lengthDif, tweenLength, oldClass, newClass, rightToLeft, fillChar, speed, original, hasClass } = data,\n\t\t\tl = text.length,\n\t\t\ttime = tween._time,\n\t\t\ttimeDif = time - prevScrambleTime,\n\t\t\ti, i2, startText, endText, applyNew, applyOld, str, startClass, endClass, position, r;\n\t\tif (revealDelay) {\n\t\t\tif (tween._from) {\n\t\t\t\ttime = tween._dur - time; //invert the time for from() tweens\n\t\t\t}\n\t\t\tratio = (time === 0) ? 0 : (time < revealDelay) ? 0.000001 : (time === tween._dur) ? 1 : tween._ease((time - revealDelay) / (tween._dur - revealDelay));\n\t\t}\n\t\tif (ratio < 0) {\n\t\t\tratio = 0;\n\t\t} else if (ratio > 1) {\n\t\t\tratio = 1;\n\t\t}\n\t\tif (rightToLeft) {\n\t\t\tratio = 1 - ratio;\n\t\t}\n\t\ti = ~~(ratio * l + 0.5);\n\t\tif (ratio) {\n\t\t\tif (timeDif > speed || timeDif < -speed) {\n\t\t\t\tdata.setIndex = setIndex = (setIndex + ((Math.random() * 19) | 0)) % 20;\n\t\t\t\tdata.chars = charSet.sets[setIndex];\n\t\t\t\tdata.prevScrambleTime += timeDif;\n\t\t\t}\n\t\t\tendText = chars;\n\t\t} else {\n\t\t\tendText = original.join(delimiter);\n\t\t}\n\n\t\tr = tween._from ? ratio : 1 - ratio;\n\t\tposition = length + (tweenLength ? tween._from ? r * r * r : 1 - r * r * r : 1) * lengthDif;\n\t\tif (rightToLeft) {\n\t\t\tif (ratio === 1 && (tween._from || tween.data === \"isFromStart\")) { //special case for from() tweens\n\t\t\t\tstartText = \"\";\n\t\t\t\tendText = original.join(delimiter);\n\t\t\t} else {\n\t\t\t\tstr = text.slice(i).join(delimiter);\n\t\t\t\tif (charsHaveEmoji) {\n\t\t\t\t\tstartText = emojiSafeSplit(endText).slice(0, (position - ((textHasEmoji ? emojiSafeSplit(str) : str).length) + 0.5) | 0).join(\"\");\n\t\t\t\t} else {\n\t\t\t\t\tstartText = endText.substr(0, (position - ((textHasEmoji ? emojiSafeSplit(str) : str).length) + 0.5) | 0);\n\t\t\t\t}\n\t\t\t\tendText = str;\n\t\t\t}\n\n\t\t} else {\n\t\t\tstartText = text.slice(0, i).join(delimiter);\n\t\t\ti2 = (textHasEmoji ? emojiSafeSplit(startText) : startText).length;\n\t\t\tif (charsHaveEmoji) {\n\t\t\t\tendText = emojiSafeSplit(endText).slice(i2, (position + 0.5) | 0).join(\"\");\n\t\t\t} else {\n\t\t\t\tendText = endText.substr(i2, (position - i2 + 0.5) | 0);\n\t\t\t}\n\t\t}\n\n\t\tif (hasClass) {\n\t\t\tstartClass = rightToLeft ? oldClass : newClass;\n\t\t\tendClass = rightToLeft ? newClass : oldClass;\n\t\t\tapplyNew = (startClass && i !== 0);\n\t\t\tapplyOld = (endClass && i !== l);\n\t\t\tstr = (applyNew ? \"<span class='\" + startClass + \"'>\" : \"\") + startText + (applyNew ? \"</span>\" : \"\") + (applyOld ? \"<span class='\" + endClass + \"'>\" : \"\") + delimiter + endText + (applyOld ? \"</span>\" : \"\");\n\t\t} else {\n\t\t\tstr = startText + delimiter + endText;\n\t\t}\n\t\ttarget[prop] = (fillChar === \"&nbsp;\" && ~str.indexOf(\"  \")) ? str.split(\"  \").join(\"&nbsp;&nbsp;\") : str;\n\t}\n};\n\nScrambleTextPlugin.emojiSafeSplit = emojiSafeSplit;\nScrambleTextPlugin.getText = getText;\n\n_getGSAP() && gsap.registerPlugin(ScrambleTextPlugin);\n\nexport { ScrambleTextPlugin as default };"], "names": ["_trimExp", "emojiExp", "getText", "e", "type", "nodeType", "result", "textContent", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "nodeValue", "emojiSafeSplit", "text", "delimiter", "trim", "preserveSpaces", "unescapedCharCodes", "replace", "split", "j", "character", "l", "length", "i", "char<PERSON>t", "charCodeAt", "substr", "emoji", "push", "CharSet", "grow", "<PERSON><PERSON><PERSON><PERSON>", "sets", "_scrambleText", "this", "chars", "_getGSAP", "gsap", "window", "registerPlugin", "_initCore", "_coreInitted", "_spacesExp", "s", "Math", "random", "_upper", "_lower", "toLowerCase", "_charsLookup", "upperCase", "lowerCase", "upperAndLowerCase", "ScrambleTextPlugin", "version", "name", "register", "core", "init", "target", "value", "tween", "prop", "delim", "max<PERSON><PERSON><PERSON>", "charset", "splitByChars", "data", "original", "join", "hasClass", "newClass", "oldClass", "textHasEmoji", "chars<PERSON>ave<PERSON><PERSON><PERSON>", "lengthDif", "fillC<PERSON>", "indexOf", "charSet", "speed", "prevScrambleTime", "setIndex", "max", "<PERSON><PERSON><PERSON><PERSON>", "tween<PERSON><PERSON>th", "rightToLeft", "_props", "render", "ratio", "i2", "startText", "endText", "applyNew", "applyOld", "str", "startClass", "endClass", "position", "r", "time", "_time", "timeDif", "_from", "_dur", "_ease", "slice"], "mappings": ";;;;;;;;;6MAUA,IAAIA,EAAW,iBAEFC,EAAW,4gOAEjB,SAASC,QAAQC,OACnBC,EAAOD,EAAEE,SACZC,EAAS,MACG,IAATF,GAAuB,IAATA,GAAuB,KAATA,EAAa,IACd,iBAAnBD,EAAEI,mBACLJ,EAAEI,gBAEJJ,EAAIA,EAAEK,WAAYL,EAAGA,EAAIA,EAAEM,YAC/BH,GAAUJ,QAAQC,QAGd,GAAa,IAATC,GAAuB,IAATA,SACjBD,EAAEO,iBAEHJ,EA0CD,SAASK,eAAeC,EAAMC,EAAWC,EAAMC,EAAgBC,MACrEJ,GAAQ,GACRE,IAASF,EAAOA,EAAKE,KAAOF,EAAKE,OAASF,EAAKK,QAAQjB,EAAU,KAC7Da,GAA2B,KAAdA,SACTD,EAAKK,QAAQ,KAAM,QAAQA,QAAQ,KAAM,QAAQC,MAAML,WAK9DM,EAAGC,EAHAd,EAAS,GACZe,EAAIT,EAAKU,OACTC,EAAI,EAEEA,EAAIF,EAAGE,KAEmB,QADhCH,EAAYR,EAAKY,OAAOD,IACTE,WAAW,IAAgBL,EAAUK,WAAW,IAAM,OAAoC,OAAxBb,EAAKa,WAAWF,EAAE,IAAgBX,EAAKa,WAAWF,EAAE,IAAM,SAC1IJ,IAAMP,EAAKc,OAAOH,EAAG,IAAIL,MAAMjB,IAAa,IAAI,IAAM,IAAIqB,QAAU,EACpEF,EAAYR,EAAKc,OAAOH,EAAGJ,GAE3BI,GAAKJ,GADLb,EAAOqB,MAAQ,IAGhBrB,EAAOsB,KAAKZ,EAAqBI,EAA0B,MAAdA,EAAoB,OAAwB,MAAdA,EAAqB,QAASL,GAAgC,MAAdK,GAA2C,MAArBR,EAAKY,OAAOD,EAAE,IAAmC,MAArBX,EAAKY,OAAOD,EAAE,GAAyBH,EAAX,iBAEnMd,MC9EFuB,qBASLC,KAAA,cAAKC,OACC,IAAIR,EAAI,EAAGA,EAAI,GAAIA,SAClBS,KAAKT,IAAMU,EAAcF,EAAYG,KAAKZ,OAAQY,KAAKC,YAExDb,OAASS,6BAZHI,QACNA,MAAQxB,eAAewB,QACvBH,KAAO,QACPV,OAAS,OACT,IAAIC,EAAI,EAAGA,EAAI,GAAIA,SAClBS,KAAKT,GAAKU,EAAc,GAAIC,KAAKC,OAY7B,SAAXC,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAkB/F,SAAZG,IACCC,EAAeJ,EAAOD,IApBxB,IAAIC,EAAMI,EAGTC,EAAa,OACbT,EAAgB,SAAhBA,cAAiBX,EAAQa,WACpBd,EAAIc,EAAMb,OACbqB,EAAI,IACc,IAAVrB,GACRqB,GAAKR,KAAUS,KAAKC,SAAWxB,WAEzBsB,GAERG,EAAS,6BACTC,EAASD,EAAOE,cAChBC,EAAe,CACdC,UAAW,IAAIrB,EAAQiB,GACvBK,UAAW,IAAItB,EAAQkB,GACvBK,kBAAmB,IAAIvB,EAAQiB,EAASC,IAO7BM,EAAqB,CACjCC,QAAQ,SACRC,KAAK,eACLC,2BAASC,GACRpB,EAAOoB,EACPjB,KAEDkB,mBAAKC,EAAQC,EAAOC,MACnBpB,GAAgBD,SACXsB,KAAQ,cAAeH,EAAU,YAAe,gBAAiBA,EAAU,cAAgB,EAC3FzB,KAAK4B,WAGLH,OAASA,EACQ,iBAAXC,IACVA,EAAQ,CAAChD,KAAKgD,QAKdG,EAAOC,EAAWC,EAASC,EAHxBtD,EAAOgD,EAAMhD,MAAQgD,EAAMA,OAAS,GACvC9C,GAAuB,IAAf8C,EAAM9C,KACdqD,EAAOjC,YAERiC,EAAKtD,UAAYkD,EAAQH,EAAM/C,WAAa,GAC5CsD,EAAKC,SAAWzD,eAAeT,QAAQyD,GAAQ1C,QAAQyB,EAAY,KAAKxB,MAAM,UAAUmD,KAAK,IAAKN,EAAOjD,GAC5F,eAATF,IAAkC,IAATA,GAAyB,MAARA,IAC7CA,EAAOuD,EAAKC,SAASC,KAAKN,IAE3BI,EAAKvD,KAAOD,gBAAgBC,GAAQ,IAAIK,QAAQyB,EAAY,KAAMqB,EAAOjD,GACzEqD,EAAKG,YAAcV,EAAMW,WAAYX,EAAMY,UAC3CL,EAAKI,SAAWX,EAAMW,SACtBJ,EAAKK,SAAWZ,EAAMY,SACtBN,EAA0B,KAAVH,EAChBI,EAAKM,aAAeP,KAAkBC,EAAKvD,KAAKe,MAChDwC,EAAKO,iBAAmBd,EAAMzB,SAAWxB,eAAeiD,EAAMzB,OAAOR,MACrEwC,EAAK7C,OAAS4C,EAAeC,EAAKC,SAAS9C,OAAS6C,EAAKC,SAASC,KAAKN,GAAOzC,OAC9E6C,EAAKQ,WAAaT,EAAeC,EAAKvD,KAAKU,OAAS6C,EAAKvD,KAAKyD,KAAKN,GAAOzC,QAAU6C,EAAK7C,OACzF6C,EAAKS,SAAWhB,EAAMgB,UAAahB,EAAMzB,QAAUyB,EAAMzB,MAAM0C,QAAQ,KAAQ,SAAW,GAC1FV,EAAKW,QAAUb,EAAUhB,EAAcW,EAAMzB,OAAS,cAAiB,IAAIN,EAAQ+B,EAAMzB,OACzFgC,EAAKY,MAAQ,KAAQnB,EAAMmB,OAAS,GACpCZ,EAAKa,iBAAmB,EACxBb,EAAKc,SAA4B,GAAhBrC,KAAKC,SAAiB,GACvCmB,EAAYG,EAAK7C,OAASsB,KAAKsC,IAAIf,EAAKQ,UAAW,IACnCV,EAAQ3C,QACvB2C,EAAQnC,KAAKkC,GAEdG,EAAKhC,MAAQ8B,EAAQjC,KAAKmC,EAAKc,UAC/Bd,EAAKgB,YAAcvB,EAAMuB,aAAe,EACxChB,EAAKiB,aAAqC,IAAtBxB,EAAMwB,YAC1BjB,EAAKN,MAAQA,EACbM,EAAKkB,cAAgBzB,EAAMyB,YAC3BlB,EAAKmB,OAAO1D,KAAK,eAAgB,QAvEhB,IA0ElB2D,uBAAOC,EAAOrB,OAKZ5C,EAAGkE,EAAIC,EAAWC,EAASC,EAAUC,EAAUC,EAAKC,EAAYC,EAAUC,EAAUC,EAJ/EvC,EAAsOQ,EAAtOR,OAAQG,EAA8NK,EAA9NL,KAAMlD,EAAwNuD,EAAxNvD,KAAMC,EAAkNsD,EAAlNtD,UAAWgD,EAAuMM,EAAvMN,MAAOmB,EAAgMb,EAAhMa,iBAAkBG,EAA8KhB,EAA9KgB,YAAaF,EAAiKd,EAAjKc,SAAU9C,EAAuJgC,EAAvJhC,MAAO2C,EAAgJX,EAAhJW,QAASxD,EAAuI6C,EAAvI7C,OAAQmD,EAA+HN,EAA/HM,aAAcC,EAAiHP,EAAjHO,eAAgBC,EAAiGR,EAAjGQ,UAAWS,EAAsFjB,EAAtFiB,YAAaZ,EAAyEL,EAAzEK,SAAUD,EAA+DJ,EAA/DI,SAAUc,EAAqDlB,EAArDkB,YAAaT,EAAwCT,EAAxCS,SAAUG,EAA8BZ,EAA9BY,MAAOX,EAAuBD,EAAvBC,SAAUE,EAAaH,EAAbG,SAC9NjD,EAAIT,EAAKU,OACT6E,EAAOtC,EAAMuC,MACbC,EAAUF,EAAOnB,EAEdG,IACCtB,EAAMyC,QACTH,EAAOtC,EAAM0C,KAAOJ,GAErBX,EAAkB,IAATW,EAAc,EAAKA,EAAOhB,EAAe,KAAYgB,IAAStC,EAAM0C,KAAQ,EAAI1C,EAAM2C,OAAOL,EAAOhB,IAAgBtB,EAAM0C,KAAOpB,KAEvIK,EAAQ,EACXA,EAAQ,EACU,EAARA,IACVA,EAAQ,GAELH,IACHG,EAAQ,EAAIA,GAEbjE,KAAOiE,EAAQnE,EAAI,IAOlBsE,EANGH,IACWT,EAAVsB,GAAmBA,GAAWtB,KACjCZ,EAAKc,SAAWA,GAAYA,GAA6B,GAAhBrC,KAAKC,SAAiB,IAAM,GACrEsB,EAAKhC,MAAQ2C,EAAQ9C,KAAKiD,GAC1Bd,EAAKa,kBAAoBqB,GAEhBlE,GAEAiC,EAASC,KAAKxD,GAGzBqF,EAAIrC,EAAMyC,MAAQd,EAAQ,EAAIA,EAC9BS,EAAW3E,GAAU8D,EAAcvB,EAAMyC,MAAQJ,EAAIA,EAAIA,EAAI,EAAIA,EAAIA,EAAIA,EAAI,GAAKvB,EAYhFgB,EAXEN,EACW,IAAVG,IAAgB3B,EAAMyC,OAAwB,gBAAfzC,EAAMM,MAIxC2B,EAAMlF,EAAK6F,MAAMlF,GAAG8C,KAAKxD,GAExB6E,EADGhB,EACS/D,eAAegF,GAASc,MAAM,EAAIR,GAAaxB,EAAe9D,eAAemF,GAAOA,GAAKxE,OAAU,GAAO,GAAG+C,KAAK,IAElHsB,EAAQjE,OAAO,EAAIuE,GAAaxB,EAAe9D,eAAemF,GAAOA,GAAKxE,OAAU,GAAO,GAE9FwE,IATVJ,EAAY,GACFtB,EAASC,KAAKxD,KAYzB6E,EAAY9E,EAAK6F,MAAM,EAAGlF,GAAG8C,KAAKxD,GAClC4E,GAAMhB,EAAe9D,eAAe+E,GAAaA,GAAWpE,OACxDoD,EACO/D,eAAegF,GAASc,MAAMhB,EAAKQ,EAAW,GAAO,GAAG5B,KAAK,IAE7DsB,EAAQjE,OAAO+D,EAAKQ,EAAWR,EAAK,GAAO,IAStDK,EALGxB,IAGHsB,GAFAG,EAAaV,EAAcb,EAAWD,IAEN,GAANhD,GAER,gBAAkBwE,EAAa,KAAO,IAAML,GAAaE,EAAW,UAAY,MADlGC,GAFAG,EAAWX,EAAcd,EAAWC,IAEZjD,IAAMF,GACsF,gBAAkB2E,EAAW,KAAO,IAAMnF,EAAY8E,GAAWE,EAAW,UAAY,IAEtMH,EAAY7E,EAAY8E,EAE/BhC,EAAOG,GAAsB,WAAbc,IAA0BkB,EAAIjB,QAAQ,MAASiB,EAAI5E,MAAM,MAAMmD,KAAK,gBAAkByB,IAIxGzC,EAAmB1C,eAAiBA,eACpC0C,EAAmBnD,QAAUA,QAE7BkC,KAAcC,EAAKE,eAAec"}