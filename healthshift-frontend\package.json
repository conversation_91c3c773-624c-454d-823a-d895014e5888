{"name": "healthshift-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@headlessui/react": "^2.2.4", "@nestjs/config": "^4.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/swagger": "^11.2.0", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.81.5", "@types/node": "^24.0.10", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "socket.io-client": "^4.8.1", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.29.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/testing-library__jest-dom": "^5.14.9", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vitest": "^3.2.4"}}