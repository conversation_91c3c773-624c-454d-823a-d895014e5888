import React, { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  DollarSign, 
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  Building,
  Star
} from 'lucide-react';
import { formatCurrency, formatDateTime, getStatusColor } from '../../../lib/utils';

// Mock data - in a real app, this would come from API
const mockBookings = [
  {
    id: '1',
    shift: {
      title: 'Emergency Department RN',
      facility: {
        name: 'City General Hospital',
        rating: 4.5,
        location: 'Springfield, IL',
      },
      startDateTime: '2024-01-16T15:00:00Z',
      endDateTime: '2024-01-16T23:00:00Z',
      hourlyRate: 52.00,
    },
    status: 'confirmed',
    notes: 'Looking forward to working in the ED. I have 2 years of trauma experience.',
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-11T14:30:00Z',
  },
  {
    id: '2',
    shift: {
      title: 'ICU Night Shift',
      facility: {
        name: 'City General Hospital',
        rating: 4.5,
        location: 'Springfield, IL',
      },
      startDateTime: '2024-01-15T19:00:00Z',
      endDateTime: '2024-01-16T07:00:00Z',
      hourlyRate: 48.00,
    },
    status: 'completed',
    notes: '',
    createdAt: '2024-01-08T09:15:00Z',
    updatedAt: '2024-01-16T07:30:00Z',
  },
  {
    id: '3',
    shift: {
      title: 'Weekend Clinic Coverage',
      facility: {
        name: 'Sunnydale Family Clinic',
        rating: 4.3,
        location: 'Springfield, IL',
      },
      startDateTime: '2024-01-13T08:00:00Z',
      endDateTime: '2024-01-13T16:00:00Z',
      hourlyRate: 35.00,
    },
    status: 'pending',
    notes: 'Available for this shift. Can arrive 15 minutes early if needed.',
    createdAt: '2024-01-09T16:20:00Z',
    updatedAt: '2024-01-09T16:20:00Z',
  },
  {
    id: '4',
    shift: {
      title: 'Day Shift CNA',
      facility: {
        name: 'Greenwood Nursing Home',
        rating: 4.2,
        location: 'Springfield, IL',
      },
      startDateTime: '2024-01-12T07:00:00Z',
      endDateTime: '2024-01-12T15:00:00Z',
      hourlyRate: 24.00,
    },
    status: 'cancelled',
    notes: 'Had to cancel due to family emergency.',
    createdAt: '2024-01-05T11:45:00Z',
    updatedAt: '2024-01-11T08:00:00Z',
  },
];

const statusFilters = [
  'All Status',
  'Pending',
  'Confirmed',
  'Completed',
  'Cancelled',
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'confirmed':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'completed':
      return <CheckCircle className="h-5 w-5 text-blue-500" />;
    case 'pending':
      return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    case 'cancelled':
      return <XCircle className="h-5 w-5 text-red-500" />;
    default:
      return <AlertCircle className="h-5 w-5 text-gray-500" />;
  }
};

export function BookingHistory() {
  const [statusFilter, setStatusFilter] = useState('All Status');
  const [sortBy, setSortBy] = useState('date');

  const calculateDuration = (start: string, end: string) => {
    const startTime = new Date(start);
    const endTime = new Date(end);
    const hours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    return hours;
  };

  const calculateEarnings = (rate: number, duration: number) => {
    return rate * duration;
  };

  const filteredBookings = mockBookings.filter(booking => {
    if (statusFilter === 'All Status') return true;
    return booking.status.toLowerCase() === statusFilter.toLowerCase();
  });

  const totalEarnings = filteredBookings
    .filter(booking => booking.status === 'completed')
    .reduce((total, booking) => {
      const duration = calculateDuration(booking.shift.startDateTime, booking.shift.endDateTime);
      return total + calculateEarnings(booking.shift.hourlyRate, duration);
    }, 0);

  const completedShifts = filteredBookings.filter(booking => booking.status === 'completed').length;

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatCurrency(totalEarnings)}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed Shifts</p>
              <p className="text-2xl font-semibold text-gray-900">
                {completedShifts}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Bookings</p>
              <p className="text-2xl font-semibold text-gray-900">
                {filteredBookings.length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="card">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Filter className="h-5 w-5 text-gray-400 mr-2" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="input"
              >
                {statusFilters.map((status) => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input"
            >
              <option value="date">Sort by Date</option>
              <option value="status">Sort by Status</option>
              <option value="earnings">Sort by Earnings</option>
            </select>
          </div>
        </div>
      </div>

      {/* Booking Cards */}
      <div className="space-y-4">
        {filteredBookings.map((booking) => {
          const duration = calculateDuration(booking.shift.startDateTime, booking.shift.endDateTime);
          const earnings = calculateEarnings(booking.shift.hourlyRate, duration);
          
          return (
            <div key={booking.id} className="card">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {booking.shift.title}
                      </h3>
                      <div className="flex items-center text-gray-600 mb-2">
                        <Building className="h-4 w-4 mr-1" />
                        <span className="font-medium">{booking.shift.facility.name}</span>
                        <span className="mx-2">•</span>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 mr-1" />
                          <span>{booking.shift.facility.rating}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(booking.status)}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                        {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDateTime(booking.shift.startDateTime)}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {duration} hours
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {booking.shift.facility.location}
                    </div>
                  </div>
                  
                  {booking.notes && (
                    <div className="bg-gray-50 rounded-lg p-3 mb-3">
                      <p className="text-sm text-gray-700">
                        <strong>Notes:</strong> {booking.notes}
                      </p>
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-500">
                    Applied: {formatDateTime(booking.createdAt)}
                    {booking.updatedAt !== booking.createdAt && (
                      <span> • Updated: {formatDateTime(booking.updatedAt)}</span>
                    )}
                  </div>
                </div>
                
                <div className="lg:ml-6 lg:text-right mt-4 lg:mt-0">
                  <div className="flex lg:flex-col items-center lg:items-end justify-between lg:justify-start">
                    <div>
                      <div className="text-lg font-semibold text-gray-900">
                        {formatCurrency(booking.shift.hourlyRate)}/hr
                      </div>
                      <div className="text-sm text-gray-500">
                        Total: {formatCurrency(earnings)}
                      </div>
                    </div>
                    
                    {booking.status === 'pending' && (
                      <div className="flex space-x-2 mt-2 lg:mt-4">
                        <button className="btn-outline text-sm px-3 py-1">
                          Edit
                        </button>
                        <button className="text-red-600 hover:text-red-700 text-sm px-3 py-1">
                          Cancel
                        </button>
                      </div>
                    )}
                    
                    {booking.status === 'confirmed' && (
                      <div className="mt-2 lg:mt-4">
                        <button className="btn-outline text-sm px-3 py-1">
                          View Details
                        </button>
                      </div>
                    )}
                    
                    {booking.status === 'completed' && (
                      <div className="mt-2 lg:mt-4">
                        <button className="btn-outline text-sm px-3 py-1">
                          Rate & Review
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredBookings.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
          <p className="text-gray-500 mb-4">
            {statusFilter === 'All Status' 
              ? "You haven't booked any shifts yet." 
              : `No ${statusFilter.toLowerCase()} bookings found.`}
          </p>
          <button className="btn-primary">
            Find Shifts to Book
          </button>
        </div>
      )}
    </div>
  );
}
