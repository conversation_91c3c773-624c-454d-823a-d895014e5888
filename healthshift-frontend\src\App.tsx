import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationsProvider } from './contexts/NotificationsContext';
import { ProtectedRoute, PublicRoute } from './components/auth/ProtectedRoute';
import { LoginForm } from './components/auth/LoginForm';
import { RegisterForm } from './components/auth/RegisterForm';
import { SupabaseTest } from './components/SupabaseTest';
import { validateEnv } from './config/env';

// Lazy load dashboard components
const ProfessionalDashboard = React.lazy(() =>
  import('./components/dashboard/ProfessionalDashboard')
);

const FacilityDashboard = React.lazy(() =>
  import('./components/dashboard/FacilityDashboard')
);

const LandingPage = React.lazy(() =>
  import('./components/landing/LandingPage')
);

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Validate environment variables on app start
validateEnv();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NotificationsProvider>
          <Router>
          <div className="min-h-screen bg-gray-50">
            <React.Suspense
              fallback={
                <div className="min-h-screen flex items-center justify-center">
                  <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
                </div>
              }
            >
              <Routes>
                {/* Public routes */}
                <Route
                  path="/"
                  element={
                    <PublicRoute>
                      <LandingPage />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/login"
                  element={
                    <PublicRoute>
                      <LoginForm />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/register"
                  element={
                    <PublicRoute>
                      <RegisterForm />
                    </PublicRoute>
                  }
                />

                {/* Protected routes for healthcare professionals */}
                <Route
                  path="/professional/*"
                  element={
                    <ProtectedRoute requiredRole="professional">
                      <ProfessionalDashboard />
                    </ProtectedRoute>
                  }
                />

                {/* Protected routes for facilities */}
                <Route
                  path="/facility/*"
                  element={
                    <ProtectedRoute requiredRole="facility">
                      <FacilityDashboard />
                    </ProtectedRoute>
                  }
                />

                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </React.Suspense>
          </div>
        </Router>
        <SupabaseTest />
        </NotificationsProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
