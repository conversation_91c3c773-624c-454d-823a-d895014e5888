# HealthShift Deployment Guide

This guide covers deploying HealthShift to AWS using modern cloud infrastructure practices.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CloudFront    │    │  Application     │    │   Database      │
│   (Frontend)    │────│  Load Balancer   │────│   (RDS)         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│      S3         │    │   ECS Fargate    │    │   ElastiCache   │
│   (Static)      │    │   (Backend)      │    │   (Sessions)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

- AWS CLI configured with appropriate permissions
- Docker installed locally
- Node.js 18+ for local builds
- Domain name (optional, for custom domains)

## 🚀 Deployment Steps

### 1. Database Setup (RDS PostgreSQL)

```bash
# Create RDS PostgreSQL instance
aws rds create-db-instance \
  --db-instance-identifier healthshift-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --engine-version 14.9 \
  --master-username healthshift \
  --master-user-password YOUR_SECURE_PASSWORD \
  --allocated-storage 20 \
  --vpc-security-group-ids sg-xxxxxxxxx \
  --db-subnet-group-name default \
  --backup-retention-period 7 \
  --storage-encrypted
```

### 2. Backend Deployment (ECS Fargate)

#### Create Dockerfile for Backend

```dockerfile
# healthshift-backend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runtime

WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./

EXPOSE 3001

CMD ["node", "dist/main"]
```

#### Build and Push to ECR

```bash
# Create ECR repository
aws ecr create-repository --repository-name healthshift-backend

# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin YOUR_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com

# Build and tag image
cd healthshift-backend
docker build -t healthshift-backend .
docker tag healthshift-backend:latest YOUR_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/healthshift-backend:latest

# Push to ECR
docker push YOUR_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/healthshift-backend:latest
```

#### ECS Task Definition

```json
{
  "family": "healthshift-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "healthshift-backend",
      "image": "YOUR_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/healthshift-backend:latest",
      "portMappings": [
        {
          "containerPort": 3001,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "PORT",
          "value": "3001"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:healthshift/database-url"
        },
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:healthshift/jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/healthshift-backend",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Create ECS Service

```bash
# Create ECS cluster
aws ecs create-cluster --cluster-name healthshift-cluster

# Register task definition
aws ecs register-task-definition --cli-input-json file://task-definition.json

# Create service
aws ecs create-service \
  --cluster healthshift-cluster \
  --service-name healthshift-backend-service \
  --task-definition healthshift-backend:1 \
  --desired-count 2 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-xxxxxxxxx,subnet-yyyyyyyyy],securityGroups=[sg-xxxxxxxxx],assignPublicIp=ENABLED}" \
  --load-balancers targetGroupArn=arn:aws:elasticloadbalancing:us-east-1:YOUR_ACCOUNT_ID:targetgroup/healthshift-backend/xxxxxxxxx,containerName=healthshift-backend,containerPort=3001
```

### 3. Frontend Deployment (S3 + CloudFront)

#### Build Frontend

```bash
cd healthshift-frontend

# Install dependencies
npm install

# Build for production
npm run build
```

#### Create S3 Bucket and Upload

```bash
# Create S3 bucket
aws s3 mb s3://healthshift-frontend-bucket

# Configure bucket for static website hosting
aws s3 website s3://healthshift-frontend-bucket --index-document index.html --error-document index.html

# Upload build files
aws s3 sync dist/ s3://healthshift-frontend-bucket --delete

# Set public read permissions
aws s3api put-bucket-policy --bucket healthshift-frontend-bucket --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::healthshift-frontend-bucket/*"
    }
  ]
}'
```

#### Create CloudFront Distribution

```json
{
  "CallerReference": "healthshift-frontend-2024",
  "Comment": "HealthShift Frontend Distribution",
  "DefaultRootObject": "index.html",
  "Origins": {
    "Quantity": 1,
    "Items": [
      {
        "Id": "S3-healthshift-frontend-bucket",
        "DomainName": "healthshift-frontend-bucket.s3.amazonaws.com",
        "S3OriginConfig": {
          "OriginAccessIdentity": ""
        }
      }
    ]
  },
  "DefaultCacheBehavior": {
    "TargetOriginId": "S3-healthshift-frontend-bucket",
    "ViewerProtocolPolicy": "redirect-to-https",
    "TrustedSigners": {
      "Enabled": false,
      "Quantity": 0
    },
    "ForwardedValues": {
      "QueryString": false,
      "Cookies": {
        "Forward": "none"
      }
    },
    "MinTTL": 0,
    "DefaultTTL": 86400,
    "MaxTTL": 31536000
  },
  "CustomErrorPages": {
    "Quantity": 1,
    "Items": [
      {
        "ErrorCode": 404,
        "ResponsePagePath": "/index.html",
        "ResponseCode": "200",
        "ErrorCachingMinTTL": 300
      }
    ]
  },
  "Enabled": true,
  "PriceClass": "PriceClass_100"
}
```

### 4. Load Balancer Configuration

#### Application Load Balancer for Backend

```bash
# Create target group
aws elbv2 create-target-group \
  --name healthshift-backend-tg \
  --protocol HTTP \
  --port 3001 \
  --vpc-id vpc-xxxxxxxxx \
  --target-type ip \
  --health-check-path /api/health

# Create load balancer
aws elbv2 create-load-balancer \
  --name healthshift-alb \
  --subnets subnet-xxxxxxxxx subnet-yyyyyyyyy \
  --security-groups sg-xxxxxxxxx

# Create listener
aws elbv2 create-listener \
  --load-balancer-arn arn:aws:elasticloadbalancing:us-east-1:YOUR_ACCOUNT_ID:loadbalancer/app/healthshift-alb/xxxxxxxxx \
  --protocol HTTP \
  --port 80 \
  --default-actions Type=forward,TargetGroupArn=arn:aws:elasticloadbalancing:us-east-1:YOUR_ACCOUNT_ID:targetgroup/healthshift-backend-tg/xxxxxxxxx
```

### 5. SSL/TLS Configuration

#### Request SSL Certificate

```bash
# Request certificate
aws acm request-certificate \
  --domain-name api.healthshift.com \
  --subject-alternative-names healthshift.com www.healthshift.com \
  --validation-method DNS

# Add HTTPS listener to load balancer
aws elbv2 create-listener \
  --load-balancer-arn arn:aws:elasticloadbalancing:us-east-1:YOUR_ACCOUNT_ID:loadbalancer/app/healthshift-alb/xxxxxxxxx \
  --protocol HTTPS \
  --port 443 \
  --certificates CertificateArn=arn:aws:acm:us-east-1:YOUR_ACCOUNT_ID:certificate/xxxxxxxxx \
  --default-actions Type=forward,TargetGroupArn=arn:aws:elasticloadbalancing:us-east-1:YOUR_ACCOUNT_ID:targetgroup/healthshift-backend-tg/xxxxxxxxx
```

### 6. Environment Variables and Secrets

#### Store Secrets in AWS Secrets Manager

```bash
# Database URL
aws secretsmanager create-secret \
  --name healthshift/database-url \
  --description "Database connection string" \
  --secret-string "postgresql://username:<EMAIL>:5432/healthshift"

# JWT Secret
aws secretsmanager create-secret \
  --name healthshift/jwt-secret \
  --description "JWT signing secret" \
  --secret-string "your-super-secure-jwt-secret-key"
```

### 7. Monitoring and Logging

#### CloudWatch Log Groups

```bash
# Create log group for backend
aws logs create-log-group --log-group-name /ecs/healthshift-backend

# Create log group for frontend (CloudFront logs)
aws logs create-log-group --log-group-name /aws/cloudfront/healthshift-frontend
```

#### CloudWatch Alarms

```bash
# High CPU alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "HealthShift-Backend-HighCPU" \
  --alarm-description "Alarm when CPU exceeds 70%" \
  --metric-name CPUUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 70 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2

# High memory alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "HealthShift-Backend-HighMemory" \
  --alarm-description "Alarm when memory exceeds 80%" \
  --metric-name MemoryUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2
```

## 🔧 Configuration

### Backend Environment Variables

```env
NODE_ENV=production
PORT=3001
DATABASE_URL=postgresql://username:<EMAIL>:5432/healthshift
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=7d
FRONTEND_URL=https://healthshift.com
```

### Frontend Environment Variables

```env
VITE_API_BASE_URL=https://api.healthshift.com/api
VITE_WS_URL=https://api.healthshift.com
```

## 🚀 CI/CD Pipeline (GitHub Actions)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to AWS

on:
  push:
    branches: [main]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push backend image
        working-directory: ./healthshift-backend
        run: |
          docker build -t healthshift-backend .
          docker tag healthshift-backend:latest $ECR_REGISTRY/healthshift-backend:latest
          docker push $ECR_REGISTRY/healthshift-backend:latest

      - name: Update ECS service
        run: |
          aws ecs update-service --cluster healthshift-cluster --service healthshift-backend-service --force-new-deployment

  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install and build frontend
        working-directory: ./healthshift-frontend
        run: |
          npm install
          npm run build

      - name: Deploy to S3
        working-directory: ./healthshift-frontend
        run: |
          aws s3 sync dist/ s3://healthshift-frontend-bucket --delete

      - name: Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"
```

## 📊 Monitoring and Maintenance

### Health Checks

- **Backend**: `/api/health` endpoint
- **Frontend**: CloudFront origin health checks
- **Database**: RDS monitoring and automated backups

### Scaling

- **Backend**: ECS auto-scaling based on CPU/memory
- **Frontend**: CloudFront global edge locations
- **Database**: RDS read replicas for read scaling

### Backup Strategy

- **Database**: Automated daily backups with 7-day retention
- **Application**: Blue-green deployments for zero downtime
- **Static Assets**: S3 versioning enabled

## 🔒 Security Considerations

- **Network**: VPC with private subnets for backend and database
- **Access**: IAM roles with least privilege principle
- **Encryption**: SSL/TLS for all communications, encrypted RDS storage
- **Secrets**: AWS Secrets Manager for sensitive configuration
- **Monitoring**: CloudTrail for API auditing

## 💰 Cost Optimization

- **Compute**: Use Fargate Spot for non-critical workloads
- **Storage**: S3 Intelligent Tiering for static assets
- **CDN**: CloudFront with appropriate caching policies
- **Database**: Right-size RDS instances based on usage

---

This deployment guide provides a production-ready setup for HealthShift on AWS. Adjust configurations based on your specific requirements and compliance needs.
