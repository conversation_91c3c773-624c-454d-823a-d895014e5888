{"version": 3, "file": "EasePack.min.js", "sources": ["../src/EasePack.js"], "sourcesContent": ["/*!\n * EasePack 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _registerEase,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_boolean = (value, defaultValue) => !!(typeof(value) === \"undefined\" ? defaultValue : value && !~((value + \"\").indexOf(\"false\"))),\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (gsap) {\n\t\t\t_registerEase = gsap.registerEase;\n\t\t\t//add weighted ease capabilities to standard eases so users can do \"power2.inOut(0.8)\" for example to push everything toward the \"out\", or (-0.8) to push it toward the \"in\" (0 is neutral)\n\t\t\tlet eases = gsap.parseEase(),\n\t\t\t\tcreateConfig = ease => ratio => {\n\t\t\t\t\tlet y = 0.5 + ratio / 2;\n\t\t\t\t\tease.config = p => ease(2 * (1 - p) * p * y + p * p);\n\t\t\t\t},\n\t\t\t\tp;\n\t\t\tfor (p in eases) {\n\t\t\t\tif (!eases[p].config) {\n\t\t\t\t\tcreateConfig(eases[p]);\n\t\t\t\t}\n\t\t\t}\n\t\t\t_registerEase(\"slow\", SlowMo);\n\t\t\t_registerEase(\"expoScale\", ExpoScaleEase);\n\t\t\t_registerEase(\"rough\", RoughEase);\n\t\t\tfor (p in EasePack) {\n\t\t\t\tp !== \"version\" && gsap.core.globals(p, EasePack[p]);\n\t\t\t}\n\t\t\t_coreInitted = 1;\n\t\t}\n\t},\n\t_createSlowMo = (linearRatio, power, yoyoMode) => {\n\t\tlinearRatio = Math.min(1, linearRatio || 0.7);\n\t\tlet pow = linearRatio < 1 ? ((power || power === 0) ? power : 0.7) : 0,\n\t\t\tp1 = (1 - linearRatio) / 2,\n\t\t\tp3 = p1 + linearRatio,\n\t\t\tcalcEnd = _boolean(yoyoMode);\n\t\treturn p => {\n\t\t\tlet r = p + (0.5 - p) * pow;\n\t\t\treturn (p < p1) ? (calcEnd ? 1 - ((p = 1 - (p / p1)) * p) : r - ((p = 1 - (p / p1)) * p * p * p * r)) : (p > p3) ? (calcEnd ? (p === 1 ? 0 : 1 - (p = (p - p3) / p1) * p) : r + ((p - r) * (p = (p - p3) / p1) * p * p * p)) : (calcEnd ? 1 : r);\n\t\t}\n\t},\n\t_createExpoScale = (start, end, ease) => {\n\t\tlet p1 = Math.log(end / start),\n\t\t\tp2 = end - start;\n\t\tease && (ease = gsap.parseEase(ease));\n\t\treturn p => (start * Math.exp(p1 * (ease ? ease(p) : p)) - start) / p2;\n\t},\n\tEasePoint = function(time, value, next) {\n\t\tthis.t = time;\n\t\tthis.v = value;\n\t\tif (next) {\n\t\t\tthis.next = next;\n\t\t\tnext.prev = this;\n\t\t\tthis.c = next.v - value;\n\t\t\tthis.gap = next.t - time;\n\t\t}\n\t},\n\t_createRoughEase = vars => {\n\t\tif (typeof(vars) !== \"object\") { //users may pass in via a string, like \"rough(30)\"\n\t\t\tvars = {points: +vars || 20};\n\t\t}\n\t\tlet taper = vars.taper || \"none\",\n\t\t\ta = [],\n\t\t\tcnt = 0,\n\t\t\tpoints = (+vars.points || 20) | 0,\n\t\t\ti = points,\n\t\t\trandomize = _boolean(vars.randomize, true),\n\t\t\tclamp = _boolean(vars.clamp),\n\t\t\ttemplate = gsap ? gsap.parseEase(vars.template) : 0,\n\t\t\tstrength = (+vars.strength || 1) * 0.4,\n\t\t\tx, y, bump, invX, obj, pnt, recent;\n\t\twhile (--i > -1) {\n\t\t\tx = randomize ? Math.random() : (1 / points) * i;\n\t\t\ty = template ? template(x) : x;\n\t\t\tif (taper === \"none\") {\n\t\t\t\tbump = strength;\n\t\t\t} else if (taper === \"out\") {\n\t\t\t\tinvX = 1 - x;\n\t\t\t\tbump = invX * invX * strength;\n\t\t\t} else if (taper === \"in\") {\n\t\t\t\tbump = x * x * strength;\n\t\t\t} else if (x < 0.5) {  //\"both\" (start)\n\t\t\t\tinvX = x * 2;\n\t\t\t\tbump = invX * invX * 0.5 * strength;\n\t\t\t} else {\t\t\t\t//\"both\" (end)\n\t\t\t\tinvX = (1 - x) * 2;\n\t\t\t\tbump = invX * invX * 0.5 * strength;\n\t\t\t}\n\t\t\tif (randomize) {\n\t\t\t\ty += (Math.random() * bump) - (bump * 0.5);\n\t\t\t} else if (i % 2) {\n\t\t\t\ty += bump * 0.5;\n\t\t\t} else {\n\t\t\t\ty -= bump * 0.5;\n\t\t\t}\n\t\t\tif (clamp) {\n\t\t\t\tif (y > 1) {\n\t\t\t\t\ty = 1;\n\t\t\t\t} else if (y < 0) {\n\t\t\t\t\ty = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\ta[cnt++] = {x:x, y:y};\n\t\t}\n\t\ta.sort((a, b) => a.x - b.x);\n\t\tpnt = new EasePoint(1, 1, null);\n\t\ti = points;\n\t\twhile (i--) {\n\t\t\tobj = a[i];\n\t\t\tpnt = new EasePoint(obj.x, obj.y, pnt);\n\t\t}\n\t\trecent = new EasePoint(0, 0, pnt.t ? pnt : pnt.next);\n\t\treturn p => {\n\t\t\tlet pnt = recent;\n\t\t\tif (p > pnt.t) {\n\t\t\t\twhile (pnt.next && p >= pnt.t) {\n\t\t\t\t\tpnt = pnt.next;\n\t\t\t\t}\n\t\t\t\tpnt = pnt.prev;\n\t\t\t} else {\n\t\t\t\twhile (pnt.prev && p <= pnt.t) {\n\t\t\t\t\tpnt = pnt.prev;\n\t\t\t\t}\n\t\t\t}\n\t\t\trecent = pnt;\n\t\t\treturn pnt.v + ((p - pnt.t) / pnt.gap) * pnt.c;\n\t\t};\n\t};\n\nexport const SlowMo = _createSlowMo(0.7);\nSlowMo.ease = SlowMo; //for backward compatibility\nSlowMo.config = _createSlowMo;\n\nexport const ExpoScaleEase = _createExpoScale(1, 2);\nExpoScaleEase.config = _createExpoScale;\n\nexport const RoughEase = _createRoughEase();\nRoughEase.ease = RoughEase; //for backward compatibility\nRoughEase.config = _createRoughEase;\n\nexport const EasePack = {\n\tSlowMo: SlowMo,\n\tRoughEase: RoughEase,\n\tExpoScaleEase: ExpoScaleEase\n};\n\nfor (let p in EasePack) {\n\tEasePack[p].register = _initCore;\n\tEasePack[p].version = \"3.13.0\";\n}\n\n_getGSAP() && gsap.registerPlugin(SlowMo);\n\nexport { EasePack as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_boolean", "value", "defaultValue", "indexOf", "_initCore", "core", "_registerEase", "registerEase", "p", "eases", "parseEase", "createConfig", "ease", "ratio", "y", "config", "SlowMo", "ExpoScaleEase", "RoughEase", "<PERSON>ase<PERSON><PERSON>", "globals", "_createSlowMo", "linearRatio", "power", "yoyoMode", "pow", "Math", "min", "p1", "p3", "calcEnd", "r", "_createExpoScale", "start", "end", "log", "p2", "exp", "EasePoint", "time", "next", "t", "v", "prev", "this", "c", "gap", "_createRoughEase", "vars", "points", "x", "bump", "invX", "obj", "pnt", "recent", "taper", "a", "cnt", "i", "randomize", "clamp", "template", "strength", "random", "sort", "b", "register", "version"], "mappings": ";;;;;;;;;6MAWY,SAAXA,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAChG,SAAXG,EAAYC,EAAOC,iBAAsC,IAAXD,EAAyBC,EAAeD,MAAaA,EAAQ,IAAIE,QAAQ,UAC3G,SAAZC,EAAYC,MACXR,EAAOQ,GAAQT,IACL,CACTU,EAAgBT,EAAKU,iBAOpBC,EALGC,EAAQZ,EAAKa,YAChBC,EAAe,SAAfA,aAAeC,UAAQ,SAAAC,OAClBC,EAAI,GAAMD,EAAQ,EACtBD,EAAKG,OAAS,SAAAP,UAAKI,EAAK,GAAK,EAAIJ,GAAKA,EAAIM,EAAIN,EAAIA,UAG/CA,KAAKC,EACJA,EAAMD,GAAGO,QACbJ,EAAaF,EAAMD,QAMhBA,KAHLF,EAAc,OAAQU,GACtBV,EAAc,YAAaW,GAC3BX,EAAc,QAASY,GACbC,EACH,YAANX,GAAmBX,EAAKQ,KAAKe,QAAQZ,EAAGW,EAASX,KAKpC,SAAhBa,EAAiBC,EAAaC,EAAOC,OAEhCC,GADJH,EAAcI,KAAKC,IAAI,EAAGL,GAAe,KACjB,EAAMC,GAAmB,IAAVA,EAAeA,EAAQ,GAAO,EACpEK,GAAM,EAAIN,GAAe,EACzBO,EAAKD,EAAKN,EACVQ,EAAU9B,EAASwB,UACb,SAAAhB,OACFuB,EAAIvB,GAAK,GAAMA,GAAKiB,SAChBjB,EAAIoB,EAAOE,EAAU,GAAMtB,EAAI,EAAKA,EAAIoB,GAAOpB,EAAKuB,GAAMvB,EAAI,EAAKA,EAAIoB,GAAOpB,EAAIA,EAAIA,EAAIuB,EAAWF,EAAJrB,EAAWsB,EAAiB,IAANtB,EAAU,EAAI,GAAKA,GAAKA,EAAIqB,GAAMD,GAAMpB,EAAKuB,GAAMvB,EAAIuB,IAAMvB,GAAKA,EAAIqB,GAAMD,GAAMpB,EAAIA,EAAIA,EAAOsB,EAAU,EAAIC,GAG7N,SAAnBC,EAAoBC,EAAOC,EAAKtB,OAC3BgB,EAAKF,KAAKS,IAAID,EAAMD,GACvBG,EAAKF,EAAMD,SACHrB,EAATA,GAAgBf,EAAKa,UAAUE,GACxB,SAAAJ,UAAMyB,EAAQP,KAAKW,IAAIT,GAAMhB,EAAOA,EAAKJ,GAAKA,IAAMyB,GAASG,GAEzD,SAAZE,EAAqBC,EAAMtC,EAAOuC,QAC5BC,EAAIF,OACJG,EAAIzC,EACLuC,WACEA,KAAOA,GACPG,KAAOC,MACPC,EAAIL,EAAKE,EAAIzC,OACb6C,IAAMN,EAAKC,EAAIF,GAGH,SAAnBQ,EAAmBC,GACG,iBAAVA,IACVA,EAAO,CAACC,QAASD,GAAQ,aAWzBE,EAAGpC,EAAGqC,EAAMC,EAAMC,EAAKC,EAAKC,EATzBC,EAAQR,EAAKQ,OAAS,OACzBC,EAAI,GACJC,EAAM,EACNT,EAAgC,IAArBD,EAAKC,QAAU,IAC1BU,EAAIV,EACJW,EAAY5D,EAASgD,EAAKY,WAAW,GACrCC,EAAQ7D,EAASgD,EAAKa,OACtBC,EAAWjE,EAAOA,EAAKa,UAAUsC,EAAKc,UAAY,EAClDC,EAAmC,KAAtBf,EAAKe,UAAY,IAEjB,IAALJ,GACRT,EAAIU,EAAYlC,KAAKsC,SAAY,EAAIf,EAAUU,EAC/C7C,EAAIgD,EAAWA,EAASZ,GAAKA,EAE5BC,EADa,SAAVK,EACIO,EACa,QAAVP,GACVJ,EAAO,EAAIF,GACGE,EAAOW,EACD,OAAVP,EACHN,EAAIA,EAAIa,EACLb,EAAI,IACdE,EAAW,EAAJF,GACOE,EAAO,GAAMW,GAE3BX,EAAiB,GAAT,EAAIF,IACEE,EAAO,GAAMW,EAExBH,EACH9C,GAAMY,KAAKsC,SAAWb,EAAgB,GAAPA,EACrBQ,EAAI,EACd7C,GAAY,GAAPqC,EAELrC,GAAY,GAAPqC,EAEFU,IACK,EAAJ/C,EACHA,EAAI,EACMA,EAAI,IACdA,EAAI,IAGN2C,EAAEC,KAAS,CAACR,EAAEA,EAAGpC,EAAEA,OAEpB2C,EAAEQ,KAAK,SAACR,EAAGS,UAAMT,EAAEP,EAAIgB,EAAEhB,IACzBI,EAAM,IAAIhB,EAAU,EAAG,EAAG,MAC1BqB,EAAIV,EACGU,KACNN,EAAMI,EAAEE,GACRL,EAAM,IAAIhB,EAAUe,EAAIH,EAAGG,EAAIvC,EAAGwC,UAEnCC,EAAS,IAAIjB,EAAU,EAAG,EAAGgB,EAAIb,EAAIa,EAAMA,EAAId,MACxC,SAAAhC,OACF8C,EAAMC,KACN/C,EAAI8C,EAAIb,EAAG,MACPa,EAAId,MAAQhC,GAAK8C,EAAIb,GAC3Ba,EAAMA,EAAId,KAEXc,EAAMA,EAAIX,eAEHW,EAAIX,MAAQnC,GAAK8C,EAAIb,GAC3Ba,EAAMA,EAAIX,YAGZY,EAASD,GACEZ,GAAMlC,EAAI8C,EAAIb,GAAKa,EAAIR,IAAOQ,EAAIT,OA3H5ChD,EAAoBS,EA+HXU,EAASK,EAAc,KACpCL,EAAOJ,KAAOI,GACPD,OAASM,MAEHJ,EAAgBe,EAAiB,EAAG,GACjDf,EAAcF,OAASiB,MAEVd,EAAY6B,KACzB7B,EAAUN,KAAOM,GACPH,OAASgC,MAEN5B,EAAW,CACvBH,OAAQA,EACRE,UAAWA,EACXD,cAAeA,GAGhB,IAAK,IAAIT,KAAKW,EACbA,EAASX,GAAG2D,SAAW/D,EACvBe,EAASX,GAAG4D,QAAU,SAGvBxE,KAAcC,EAAKE,eAAeiB"}