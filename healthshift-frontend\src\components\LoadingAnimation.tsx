import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface LoadingAnimationProps {
  isVisible: boolean;
  onComplete?: () => void;
}

const LoadingAnimation: React.FC<LoadingAnimationProps> = ({ isVisible, onComplete }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const barsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    if (!containerRef.current || !isVisible) return;

    const container = containerRef.current;
    const logo = logoRef.current;
    const bars = barsRef.current;

    // Create loading bars
    for (let i = 0; i < 5; i++) {
      const bar = document.createElement('div');
      bar.className = 'w-1 bg-accent rounded-full';
      bar.style.height = '0px';
      container.querySelector('.loading-bars')?.appendChild(bar);
      bars.push(bar);
      barsRef.current.push(bar);
    }

    const tl = gsap.timeline({
      onComplete: () => {
        setTimeout(() => {
          onComplete?.();
        }, 500);
      }
    });

    // Logo entrance
    tl.fromTo(logo, 
      { scale: 0, rotation: -180, opacity: 0 },
      { scale: 1, rotation: 0, opacity: 1, duration: 1, ease: "back.out(1.7)" }
    );

    // Loading bars animation
    tl.to(bars, {
      height: '40px',
      duration: 0.6,
      stagger: 0.1,
      ease: "power2.out"
    }, "-=0.5");

    // Pulsing effect
    tl.to(bars, {
      height: '20px',
      duration: 0.4,
      stagger: 0.05,
      repeat: 2,
      yoyo: true,
      ease: "power2.inOut"
    });

    // Final expansion and fade
    tl.to(bars, {
      height: '60px',
      opacity: 0,
      duration: 0.8,
      stagger: 0.1,
      ease: "power2.out"
    });

    tl.to(logo, {
      scale: 1.2,
      opacity: 0,
      duration: 0.5,
      ease: "power2.out"
    }, "-=0.3");

    tl.to(container, {
      opacity: 0,
      duration: 0.5,
      ease: "power2.out"
    });

  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  return (
    <div 
      ref={containerRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black"
    >
      <div className="text-center">
        <div 
          ref={logoRef}
          className="text-4xl font-bold text-accent mb-8"
        >
          HealthShift
        </div>
        <div className="loading-bars flex items-end justify-center space-x-2 h-16">
          {/* Bars will be created dynamically */}
        </div>
      </div>
    </div>
  );
};

export default LoadingAnimation;
