import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { MessageCircle, Phone, Mail, X } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

const FloatingActionButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const fabRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (!fabRef.current || !buttonRef.current) return;

    const fab = fabRef.current;
    const button = buttonRef.current;

    // Initial entrance animation
    gsap.fromTo(fab, 
      { 
        scale: 0, 
        rotation: -180,
        opacity: 0 
      },
      { 
        scale: 1, 
        rotation: 0,
        opacity: 1,
        duration: 1,
        ease: "back.out(1.7)",
        delay: 2
      }
    );

    // Floating animation
    gsap.to(fab, {
      y: -10,
      duration: 2,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    // Pulse effect
    gsap.to(button, {
      boxShadow: "0 0 0 0 rgba(0, 212, 170, 0.7)",
      duration: 0.6,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut"
    });

    // Hide/show on scroll
    ScrollTrigger.create({
      start: 'top -100',
      end: 'max',
      onUpdate: (self) => {
        if (self.direction === -1) {
          gsap.to(fab, { scale: 1, duration: 0.3 });
        } else {
          gsap.to(fab, { scale: 0, duration: 0.3 });
        }
      }
    });

  }, []);

  useEffect(() => {
    if (!menuRef.current) return;

    const menuItems = menuRef.current.querySelectorAll('.menu-item');

    if (isOpen) {
      gsap.fromTo(menuItems, 
        { 
          scale: 0, 
          y: 20,
          opacity: 0,
          rotation: 180
        },
        { 
          scale: 1, 
          y: 0,
          opacity: 1,
          rotation: 0,
          duration: 0.4,
          stagger: 0.1,
          ease: "back.out(1.7)"
        }
      );

      gsap.to(buttonRef.current, {
        rotation: 45,
        duration: 0.3,
        ease: "power2.out"
      });
    } else {
      gsap.to(menuItems, {
        scale: 0,
        y: 20,
        opacity: 0,
        rotation: -180,
        duration: 0.2,
        stagger: 0.05,
        ease: "power2.in"
      });

      gsap.to(buttonRef.current, {
        rotation: 0,
        duration: 0.3,
        ease: "power2.out"
      });
    }
  }, [isOpen]);

  const menuItems = [
    { icon: MessageCircle, label: 'Chat', color: 'bg-blue' },
    { icon: Phone, label: 'Call', color: 'bg-success' },
    { icon: Mail, label: 'Email', color: 'bg-warning' }
  ];

  return (
    <div 
      ref={fabRef}
      className="fixed bottom-8 right-8 z-40"
    >
      {/* Menu Items */}
      <div 
        ref={menuRef}
        className="absolute bottom-16 right-0 space-y-3"
      >
        {menuItems.map((item, index) => (
          <div
            key={index}
            className="menu-item flex items-center space-x-3"
          >
            <span className="bg-card text-white px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
              {item.label}
            </span>
            <button
              className={`w-12 h-12 ${item.color} rounded-full flex items-center justify-center text-white shadow-lg hover:scale-110 transition-transform`}
              onClick={() => console.log(`${item.label} clicked`)}
            >
              <item.icon className="h-5 w-5" />
            </button>
          </div>
        ))}
      </div>

      {/* Main FAB */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-black shadow-lg hover:scale-110 transition-transform relative overflow-hidden"
      >
        {isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <MessageCircle className="h-6 w-6" />
        )}
        
        {/* Ripple effect */}
        <div className="absolute inset-0 rounded-full bg-white opacity-0 scale-0 transition-all duration-300 hover:opacity-20 hover:scale-100" />
      </button>
    </div>
  );
};

export default FloatingActionButton;
