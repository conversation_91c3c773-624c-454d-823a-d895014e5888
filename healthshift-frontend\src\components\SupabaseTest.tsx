import React, { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';

export function SupabaseTest() {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'error'>('testing');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      // Test basic connection with sample data
      const { data, error } = await supabase
        .from('sample_shifts')
        .select('count', { count: 'exact', head: true });

      if (error) {
        throw error;
      }

      setConnectionStatus('connected');
      console.log('✅ Supabase connection successful');
      console.log('📊 Sample shifts available:', data);
    } catch (err: any) {
      setConnectionStatus('error');
      setError(err.message);
      console.error('❌ Supabase connection failed:', err);
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'testing': return 'text-yellow-600 bg-yellow-100';
      case 'connected': return 'text-green-600 bg-green-100';
      case 'error': return 'text-red-600 bg-red-100';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'testing': return 'Testing connection...';
      case 'connected': return 'Connected to Supabase';
      case 'error': return 'Connection failed';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className={`px-3 py-2 rounded-lg text-sm font-medium ${getStatusColor()}`}>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            connectionStatus === 'testing' ? 'bg-yellow-500 animate-pulse' :
            connectionStatus === 'connected' ? 'bg-green-500' : 'bg-red-500'
          }`} />
          <span>{getStatusText()}</span>
        </div>
        {error && (
          <div className="mt-1 text-xs opacity-75">
            {error}
          </div>
        )}
      </div>
    </div>
  );
}
