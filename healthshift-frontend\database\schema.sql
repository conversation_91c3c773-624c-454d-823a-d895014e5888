-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('professional', 'facility');
CREATE TYPE facility_type AS ENUM ('hospital', 'clinic', 'nursing_home', 'dental_office', 'school');
CREATE TYPE shift_status AS ENUM ('open', 'booked', 'completed', 'cancelled');
CREATE TYPE booking_status AS ENUM ('pending', 'confirmed', 'completed', 'cancelled');

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HAR(100) NOT NULL,
    role user_role NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Healthcare professionals table
CREATE TABLE healthcare_professionals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    license_number VARCHAR(100) NOT NULL UNIQUE,
    specialization VARCHAR(100) NOT NULL,
    experience INTEGER NOT NULL CHECK (experience >= 0),
    hourly_rate DECIMAL(10,2) NOT NULL CHECK (hourly_rate > 0),
    certifications TEXT[] DEFAULT '{}',
    profile_image TEXT,
    bio TEXT,
    rating DECIMAL(3,2) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
    completed_shifts INTEGER DEFAULT 0 CHECK (completed_shifts >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Facilities table
CREATE TABLE facilities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    facility_name VARCHAR(200) NOT NULL,
    facility_type facility_type NOT NULL,
    address JSONB NOT NULL,
    contact_phone VARCHAR(20) NOT NULL,
    license_number VARCHAR(100) NOT NULL UNIQUE,
    rating DECIMAL(3,2) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
    total_shifts_posted INTEGER DEFAULT 0 CHECK (total_shifts_posted >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shifts table
CREATE TABLE shifts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    facility_id UUID NOT NULL REFERENCES facilities(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    specialization VARCHAR(100) NOT NULL,
    start_date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    hourly_rate DECIMAL(10,2) NOT NULL CHECK (hourly_rate > 0),
    status shift_status DEFAULT 'open',
    requirements TEXT[] DEFAULT '{}',
    booked_by UUID REFERENCES healthcare_professionals(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_shift_duration CHECK (end_date_time > start_date_time)
);

-- Bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    shift_id UUID NOT NULL REFERENCES shifts(id) ON DELETE CASCADE,
    professional_id UUID NOT NULL REFERENCES healthcare_professionals(id) ON DELETE CASCADE,
    status booking_status DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(shift_id, professional_id)
);

-- Availability table for healthcare professionals
CREATE TABLE availability (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    professional_id UUID NOT NULL REFERENCES healthcare_professionals(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN DEFAULT FALSE,
    related_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_healthcare_professionals_user_id ON healthcare_professionals(user_id);
CREATE INDEX idx_healthcare_professionals_specialization ON healthcare_professionals(specialization);
CREATE INDEX idx_facilities_user_id ON facilities(user_id);
CREATE INDEX idx_facilities_type ON facilities(facility_type);
CREATE INDEX idx_shifts_facility_id ON shifts(facility_id);
CREATE INDEX idx_shifts_status ON shifts(status);
CREATE INDEX idx_shifts_specialization ON shifts(specialization);
CREATE INDEX idx_shifts_start_date_time ON shifts(start_date_time);
CREATE INDEX idx_bookings_shift_id ON bookings(shift_id);
CREATE INDEX idx_bookings_professional_id ON bookings(professional_id);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_availability_professional_id ON availability(professional_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(read);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_healthcare_professionals_updated_at BEFORE UPDATE ON healthcare_professionals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_facilities_updated_at BEFORE UPDATE ON facilities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shifts_updated_at BEFORE UPDATE ON shifts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_availability_updated_at BEFORE UPDATE ON availability
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE healthcare_professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE facilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE shifts ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Users can only see and update their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Healthcare professionals policies
CREATE POLICY "Healthcare professionals can view own profile" ON healthcare_professionals
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Healthcare professionals can update own profile" ON healthcare_professionals
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view healthcare professionals" ON healthcare_professionals
    FOR SELECT USING (true);

-- Facilities policies
CREATE POLICY "Facilities can view own profile" ON facilities
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Facilities can update own profile" ON facilities
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view facilities" ON facilities
    FOR SELECT USING (true);

-- Shifts policies
CREATE POLICY "Anyone can view open shifts" ON shifts
    FOR SELECT USING (true);

CREATE POLICY "Facilities can manage own shifts" ON shifts
    FOR ALL USING (auth.uid() = (SELECT user_id FROM facilities WHERE id = facility_id));

-- Bookings policies
CREATE POLICY "Users can view own bookings" ON bookings
    FOR SELECT USING (
        auth.uid() = (SELECT user_id FROM healthcare_professionals WHERE id = professional_id)
        OR auth.uid() = (SELECT f.user_id FROM facilities f JOIN shifts s ON f.id = s.facility_id WHERE s.id = shift_id)
    );

CREATE POLICY "Healthcare professionals can create bookings" ON bookings
    FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM healthcare_professionals WHERE id = professional_id));

CREATE POLICY "Users can update own bookings" ON bookings
    FOR UPDATE USING (
        auth.uid() = (SELECT user_id FROM healthcare_professionals WHERE id = professional_id)
        OR auth.uid() = (SELECT f.user_id FROM facilities f JOIN shifts s ON f.id = s.facility_id WHERE s.id = shift_id)
    );

-- Availability policies
CREATE POLICY "Healthcare professionals can manage own availability" ON availability
    FOR ALL USING (auth.uid() = (SELECT user_id FROM healthcare_professionals WHERE id = professional_id));

-- Notifications policies
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);
